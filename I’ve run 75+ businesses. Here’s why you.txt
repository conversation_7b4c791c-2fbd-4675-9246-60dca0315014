I’ve run 75+ businesses. Here’s why you’re probably chasing the wrong idea. | <PERSON> - YouTube
https://www.youtube.com/watch?v=VxAwUb86MUE

Transcript:
(00:00) You don't want to walk into the gym on  day one and try and deadlift 300 pounds. So when someone comes to me and they're  a first time entrepreneur and they say,   "I'm going to make the next great AI  company," I think that is the equivalent. I feel like you've actually started and  run more companies than maybe anyone else   in the world.
(00:21) What is your best advice  for coming up with a great startup idea? <PERSON>, <PERSON>'s longtime  business partner, has this amazing quote. Fish where the fish are. The biggest mistakes I've made have  been going into business models where   other people have repeatedly failed  and thinking, I can do this better. It's so funny to watch you on Twitter.  Clearly you've become AI obsessed.
(00:43) It's like having the world's most  reliable employee who costs $200 a month and works 24/7. So many knowledge  work jobs are going to change massively. I think the fundamental question is, do  all jobs just become a single prompt? Today, my guest is <PERSON>. <PERSON>  is the co-founder and CEO of Tiny, a holding   company that's often called the Berkshire  Hathaway of the internet.
(01:03) They own over 40 businesses ranging from Dribble to WeCommerce to  the AeroPress coffee maker, and they focused on buying profitable businesses from founders and  holding them for the long term. <PERSON> and his   co-founder bootstrapped the business from zero  to hundreds of millions of dollars in value, and <PERSON> personally was worth over $1 billion  at one point.
(01:26) In our wide-ranging conversation,   we cover a bunch of strategies for coming up with  a good business idea, what common business ideas you should avoid, his experience automating much  of his work and life using AI, and what that means for employment in the near future. Also, what  he's learned about happiness and money and how   they're not directly related, and also how getting  diagnosed with ADHD and then taking SSRIs was the thing that most impacted his happiness in life.
(01:49) This is both a powerful and also a very tactically useful conversation and I'm really excited  for you to hear it. If you enjoy this podcast,   don't forget to subscribe and follow it in  your favorite podcasting app or YouTube. Also, if you become an annual subscriber of  my newsletter, you get a year free of a   bunch of amazing products including Bolt, Linear,  Superhuman, Notion, Perplexity, Granola and more.
(02:08) Check it out at Lenny's newsletter.com and click  bundle. With that, I bring you Andrew Wilkinson. This episode is brought to you by Sauce. The way  teams turn feedback into product impact is stuck in the past, vague reports, static taxonomies,  unactionable insights that don't move business metrics. The result, churn, lost deals, misgrowth.
(02:32) Sauce is the AI product copilot that helps CPOs and product teams uncover business impact and  act faster. It listens to your sales calls,   support tickets, churn reasons, and  lost deals, surfacing the biggest product issues and opportunities in real time. It then routes them to the right teams to turn   signals into PRDs, prototypes, and even code  that drives revenue retention and adoption.
(02:49) That's why Whatnot, Linktree, Incident.io and Zip  use Sauce one enterprise uncovered a product gap that unlocked $16 million ARR, another caught a  issue and prevented millions in churn. You can too at sauce.app/lenny. Sauce built for  AI product teams, don't get left behind. This episode is brought to you by Enterpret.
(03:20) Enterpret is a customer intelligence platform   used by a leading CXN product orgs like  Canva, Notion, Perplexity, Strava, Hinge, and Linear to leverage the voice of the  customer and build best-in-class products.   Enterpret unifies all customer conversations  in real time, from Gong recordings to Zendesk, tickets to Twitter threads, and makes it available  for your team for analysis and for action. What makes Enterpret unique is its ability to  build and update a customer-specific knowledge graph that provides the most granular and  accurate categorization of all customer   feedback and connects that customer feedback  to critical metrics like revenue and CSAT.
(03:50) If modernizing your voice of customer program  to a generational upgrade is a 2025 priority, like customer-centric industry leaders  like Canva, Notion, Perplexity, and Linear,   reach out to the team at enterpret.com/lenny.  That's E-N-T-E-R-P-R-E-T.com/lenny. Andrew, thank you so much for  being here. Welcome to the podcast. Oh, thanks man, great to be here. I've been wanting to chat with you for so  long.
(04:20) There's so much that I want to talk about and I want to start with a topic that  I know that you think a lot about and that   it's also in the minds of a lot of people,  which is coming up with a great startup idea and this is something that a lot of people are  thinking about right now because AI makes it so easy to actually build your idea into something  real, and I feel like this is something you spent a lot of time thinking about.
(04:46) I feel like you've  actually started and run more companies than, I don't know, maybe anyone else in the world. I  feel like you're in the top a hundred, top 10,   something like that. I don't  know. Does that feel right? I've definitely had a lot of experiences and  I've probably started or been involved with 75 different projects or businesses where I've  been a primary contributor and I wouldn't say that's anything to brag about because I've been  an inch deep in a mile wide.
(05:09) So that's been in a lot of ways kind of my Achilles heel is I  get too excited about ideas and I start too   many businesses, but as a result I've seen  almost every business model under the sun. Amazing. Okay. Yeah, I think the benefit is for  us is we get to learn from your experience.
(05:28) Let   me just ask you this question, what is your best  advice for coming up with a great startup idea? Ultimately the best thing is something you're  going to be interested in, but I think a mistake   a lot of people make is they choose something  that everybody is interested in. So for example, they say I don't know anyone that has hadn't  had the thought, man, I would love to start   a really cool restaurant or I want to have  a cool cafe or something.
(05:50) And in reality, what they think about is how cool would it be to  come up with an amazing logo or all fun stuff, design the menu and stuff. But in reality,  operating those businesses is miserable and it's also a very hard business because every  morning millions of people wake up and they go, I should start a cafe.
(06:17) But on the flip side,  almost nobody wakes up every morning and says, "You know what? I'd love to start a funeral  home," or "I would like to start a pest control   business," or "I should start software that  helps people fill out forms for the government." And Charlie Munger, Warren Buffett's longtime  business partner, has this amazing quote. He says, "Fish where the fish are.
(06:39) " And he gives this  example, he says, "If you're a fisherman and you see a large pond and all around the pond,  there's a whole bunch of fishermen and they're all elbowing each other out of the way. They're  all using the best lures, the best fishing line, they all have amazing strategy. You actually  want to walk off into the forest and find a small fishing hole with lots of fish and very  little competition.
(07:02) And I think that's probably the most important thing in business is actually  to find those niches where you can actually make real money because competition equals lower  margin. The more competitors there are, the lower your prices have to be and the  more competitive the business is ultimately.
(07:24) It doesn't feel intuitive to go  after small markets and to find niches. So let's just follow that thread. Well,   why is that actually a source of some of the  best ideas starting really small and niche? I don't necessarily think it has to be something  that is small forever, but it has to be something that like I think about if you're a first  time entrepreneur or a student or something, you don't want to walk into the gym on day one  and try and deadlift 300 pounds.
(07:50) So when someone comes to me and they're a first time entrepreneur  and they say, "I'm going to make the next great AI   company," or "I'm going to launch a new bank," or  something like that, something that is very, very rigorous and complicated and highly competitive  and regulated, I think that is the equivalent. I think you really want to take the baby weights  and start slowly building your muscle.
(08:12) And I think about my own experience starting a business, I was  so lucky because the first business I ever started my web design agency, which became Metalab, that  was so easy and it worked immediately because all I had to do was know how to build websites  and be able to talk to potential customers. And then once they said, "Yes, I will pay you  $5,000," I just had to send them an invoice, do the work, and that was it. It was a  very simple business. And because of that,   I got immediate positive feedback and I  built my own narrative. And my narrative was,
(08:48) "I'm good at business, I can do this, keep going."  And then I went off and I started taking my money that I made from that original business and  that's when I fucked up. So I went out and I   started a pizzeria and I lost all my money.  I started a designer cat furniture business, a online DJ school, a skin cream business,  all of these things.
(09:13) I've just lost all my money almost immediately, but because I had  that first win I kept on going. And I just think it's so critical that people choose  a business where they get that initial win. Okay, this is a great topic. This is just like how  do you avoid creating a job for yourself that you hate? There's a lot of business opportunities  and ideas that like, "Yeah, you can make this   work," and then it works, and then you're  stuck doing this thing running a restaurant.
(09:36) My wife tells a story where a friend started a  coffee shop and then he's like, "I thought I was   starting a coffee shop, but I'm just replacing  milk and buying milk all day. That's my job now." So just kind of along those lines to help  people avoid creating a beast that they didn't expect any advice for Just how to know this is  maybe even though this may work and make money, you probably don't want to be spending  your life doing this sort of business. So I just started a pressure washing business.  I was speaking at a local business school and
(10:05) after I spoke, one of the kids walked up to me  and he said, "Hey, I'm an entrepreneur. I've   started two or three businesses in the past doing  landscaping and I'm not enjoying school." And so just on the spot, I said to him, "Why don't you  drop out and we'll start a business together?"   And I'd been cooking on this idea of a pressure  washing business because I'd studied that industry a little bit and I knew a few friends who'd  done it in other cities. And I had this unfair   advantage, which is I owned a whole bunch of media  properties where I could advertise it for free
(10:36) basically. And so we started this business and  I think for him, he kind of had this moment of, do I really want to be washing people's  driveways for the rest of my life?  And what I said to him is, "Look, if this gets  to scale, you'll never touch a pressure washer unless you want to.
(10:59) " If the business can  get big enough where you can have employees, he can just focus on sales or digital marketing  or whatever aspect of the business that he really loves. And I think that's the biggest thing  that a lot of entrepreneurs miss. They say, "I don't want to do that for the rest of  my life. I don't want to be in the back   of a dry cleaner dry cleaning clothes." And  to me it's just a question of scale.
(11:19) I think the cafe is a great example because a cafe, if it  doesn't get to a reasonable scale is just a job. There's a big difference between a  business and a job. I think if you   could start a pressure washing business where  you're the only employee, yeah, that's a job, but if you can get to a scale where you can  drive 10 leads a day, then you don't have to   do any of the pressure washing and you just do  what you love.
(11:44) So I think a lot of people have this kind of Protestant work ethic where they  think, well, I've got to be the person to do   everything. And I think they really need to lean  into what I call lazy leadership, which is how do I get away from the things I hate as quickly as  humanly possible? How do I be Teflon for tasks? It's interesting that this business  is non-software related at all,   this pressure washing business.
(12:08) So I guess  just for folks that are coming up with ideas, and I imagine most ideas are there's  a pull to make it software oriented,   something AI is going to help build for you and  help you run. What's your calculus on just going down a direction of real world physical business,  like a restaurant power washing business versus a software just like what are the benefits  of that direction when should someone   actually seriously think about doing a  business like a power washing business? I think if someone's listening to this podcast,  odds are they're someone who's kind of a digital
(12:36) native. And I think the question is, what's your  unfair advantage and what are you great at? So for me, I think I have reasonably good taste. And so  what I could do is I identify and I knew enough to identify great design and development talent, but  mostly I was lucky because I was a talker. I was good at meeting with clients and selling myself.
(13:03) And so ultimately my highest and best use my superpower was sales. And so that can be applied  to anything, anything where you need to go out and you need to sell a customer. And so really it just  comes down to what do you get drawn to and then how do you find the profitable business within  that? So here's an example. A friend of mine, he owns a restaurant and he said, "Oh, I love  it. I'm so passionate about it.
(13:28) It's this stunning beautiful restaurant in my hometown." And he said, "But it's really just a job for a few different people and we can't make any money at  it, but I've been noticing that there's all these vendors that service the restaurant and those guys  are making a killing." And so he told me about a business that cleans grease traps, another  one that cleans exhaust vents for kitchens.
(13:56) So I think looking around and seeing where your  passion is and then sniffing around within it, probably somewhere within your passion, there's  an opportunity. So for example, I love movies. My happy place is a dark theater. That's the  best way for me to de-stress is go to a dark   theater and watch a movie and get lost in it.
(14:20) And  probably about four or five years ago, I was like, "Man, how cool would it be to invest in movies  in some way to be a part of that creative world?" And so I started looking into funding movies  and I realized that when you fund movies, like 90% of the time you lose all your money.  And even if you do make some money, you rarely   make a good return. But I spend a bunch of time  understanding the industry and learning about it.
(14:44) And then two years ago I was in New Zealand and  I met the founder of Letterboxd and I realized, "Oh my God, this is a business where this  has a moat, so it has a network effect,   it's a huge social network for film reviewers.  It's something I'm passionate about and it's something that we can buy at a fair price."  All those things came together and I was like,   "Oh my god, I can invest in film now.
(15:10) "  In the same way I used to be a barista, we ended up buying the AeroPress coffee maker  company. So I follow my passions and spend a   long time learning different industries and  then I find the profitable niche within. Okay, and this is a great takeaway essentially  when you're thinking about startup ideas,   make sure there's some connection to  something unique to you where you have some unfair advantage. This makes me think  about Brian Armstrong. I saw him do a talk   once and he gave this really amazing insight  about why Coinbase did well and why he started
(15:39) Coinbase. And it's because if you look at  his background, he had this really rare   Venn diagram of background of computer science  and I think it was cryptography and economics, which is the exact set of skills you need to  start something like Coinbase. So I think the tip there is just what is that unique Venn  diagram of skills in your background and   just ideally what you're building connects  to that and gives you an unfair advantage.
(16:02) Well, yeah. I just met another UVic student at  the local university student. She is interested in marketing and so she's gone out and she's found  some local clients, so small restaurants and stuff and managed their social media and she said,  "Yeah, it's okay. I can make a thousand bucks a month, but it's a lot of work," and the owners  really want a lot for their money.
(16:27) And I said, "Well, if you just pivot that idea just ever  so slightly, and instead of doing restaurants, you did realtors or wealth managers who have  quite a large marketing budget and are used to spending serious money and it only has to work  a little bit to make a lot of money for them.   Those people, you can charge $5,000 a month.
(16:47) " So I think often it's you find your passion, your skill set, you zero in, and then you just  kind of pivot and you find the most profitable   way to do it. When I started my web design firm,  I started mostly with local and small projects, but very quickly I found a job board in  San Francisco where startups would share projects they needed help with. I could charge  five times the amount for five times less work.
(17:10) This point about fish where the fish are  I think is really important. You can start   something that's awesome that you love, that  you're so excited about, but nobody needs it. Talk a bit more about just what that looks  like.
(17:25) What have you seen when you're thinking   about ideas that tell you that there's fish  but also not overfished as you pointed out? I think it's hard like Warren Buffett has this  great quote, "I'm a better businessman because   I'm an investor and I'm a better investor because  I'm a businessman." And I feel like in order to know what problems are valuable to solve,  you kind of need to have valuable problems. So it's a bit of a hard thing because I  remember when I was like 20, I would say, I hate how all the cat furniture I can buy  for my cats is I bet people would pay a   lot of money to solve this, but I didn't  understand anything about the realities
(17:59) of that business model. I didn't understand  how little people were actually willing to pay. I didn't have a enough life experience  to go, "Yes, that's actually a worthwhile opportunity." And I think that being able  to know what people would pay to solve the problem realistically is incredibly valuable.
(18:24) So my example of a realtor selling a house, because I've studied that industry, I know  that a realtor can make like 20 to $50,000 selling a single house. So I can intuit that  they'd be willing to spend a lot of money if   I have a unique way of getting them clients  that'll buy houses that'll convert at a high conversion rate that is worth $5,000 a lead  maybe, right? So I think you kind of have to understand the problem in depth before you  really know. Because when I was starting out,   I would go down every rabbit trail, every  infomercial idea I had I would think was genius.
(18:59) Or people, I don't know, that don't have this  experience, which is most people. Is there one thing you do, maybe a heuristic that gives  you a sense, maybe there's something here,   maybe there's a lot of fish, maybe it's a value of  prompt or no, the soul tell me it probably isn't. No, unfortunately, I think it's mostly gut.
(19:20) I mean  Munger and Buffett talk about having a whole bunch of mental models in your brain and they form a  latticework, right? And they all kind of piece   together. And I feel like for me, there's so  much of this that is, it's almost like I'm an AI model and I've trained on all this data of  what works and what doesn't and what's a good moat and what's a bad business, et cetera. And  when I see it, I just immediately know.
(19:40) I mean, I think all entrepreneurs are so lucky now to be  able to go into Claude or ChatGPT and just say, "Hey, I'm thinking about starting a Botox  clinic. Can you break down the numbers? Is this a good business? What's hard about it? What  is the regulatory moat? What would my payroll look like?" I didn't have any of that and so I  started so many incredibly stupid businesses, but I think there's no excuse at this  point. You should be able to do it with AI. That's a really good tip actually. Then  there's this point you made about boring
(20:11) is good. That's a really good piece of advice.  A lot of people are going after flashy stuff,   things they condemn on Twitter. Your  advice here is boring is actually a really good thing because fewer people are  going after it. Is that the general tip? Totally.
(20:32) I mean, so I started this business, one  of my first called Flow and Flow was basically Asana before Asana came out. So it was a way to  manage your to-do's and projects with your team in a web app and we basically did the thing. We  made the mistake that so many entrepreneurs make, which is to go after an industry that everybody  goes after just like cafes. Everybody has the idea, man, I wish I could design my own project  management system or my own to-do system.
(21:00) And not only that, but everybody loves new things.  They love jumping around. I know me personally,   in the last three years, I've probably used three  different productivity systems and I love jumping around in them and I didn't understand that and  I ended up losing $10 million trying to compete with venture-backed businesses and bootstrapping.
(21:20) It was all my own money, poured $10 million, lit it on fire trying to compete with Asana because  I didn't understand how the business world worked really as they had raised hundreds of millions  of dollars and they were run by the co-founder of Facebook and it was a little bit like, I'm Fiji  and I'm deciding I'm going to invade the United States in retrospect, just completely silly.
(21:44) Now on the flip side, if I'd taken that same amount of energy and I'd instead focused on, let's  see, what's a really boring one I've heard about, there was a business I saw recently. They were making $30 million a year and all they did was help people fill out forms to get  government assistance in certain programs. So it'd be like your uncle is disabled and you need to  access government funding.
(22:08) The process to do it is incredibly time-consuming and miserable and  you have to fill out all these forms. They just   have software that fills out the forms for you  and it says, look, you're going to get $20,000 as a grant, pay us a thousand bucks, and that  is such a boring, nobody wakes up and goes, "I want to make form filling software," but I think  they would if they could make 20 million a year Along these lines of just bad ideas, things people  shouldn't start, what are a few ideas that you think everyone thinks is going to be a great idea  and then they do it and then they always fail? Well, I think the biggest mistake,  I can speak from my own experience,
(22:40) the biggest mistakes I've made business-wise have  been going into business models where other people have repeatedly failed and thinking,  I can do this better. So for example, me and one of my best friends about 10  years ago, we really wanted to start a bar. We just thought it'd be so cool to have a  bar where us and all of our friends could go,   we thought it'd be great for the city.
(23:10) We thought  we can run this really high margin because we're tech guys, we know how to build systems, we're  good at business, and we were utterly humbled. We realized that we didn't know anything about  business compared to someone who runs a pizzeria.   If you think about it, like my example earlier of  if you run a software company, what has to happen? You have to hire a bunch of nerds, they need  internet connections and computers and you need   to pay them and you need to coordinate online and  everyone can work asynchronously.
(23:35) Nothing has to go right. It's not that complicated, at least  at small-scale. A pizzeria, it's like if the baker doesn't wake up at three in the morning and  start prepping dough, the entire thing is effed and all along the way there's a hundred different  failure points from front of house, back of house, the deliveries, arriving on time, all this  logistics.
(23:59) And so I think it's been stuff like that. I mean also I got into the news business,  the local news business. I ended up buying an old paper in Vancouver and it's the exact same  thing. It's like you can't take a brilliant management team and change a bad business  model. Ultimately the business model wins. This is really good advice.
(24:27) Basically if  there's a bunch of dead bodies in that space,   there's probably something there that  keeps killing them that you're probably not aware of until maybe somebody figures  something out, right? Once in a while,   once in a blue moon, someone's like, "Okay,  here's how we do this and then it works." Well, look at Instacart. When Instacart came out,  everyone said, "Well, Webvan failed. This is never going to work." And we still don't necessarily  know if Instacart is a great business.
(24:49) I don't   know, I haven't studied it, but I think it was  like, okay, enough technology has changed that it can happen, but would it be easier to start  Instacart, Amazon or Coupang or would it be easier to start an enterprise SaaS software company?  Definitely the enterprise SaaS software company. I see. So this is I guess is the  advice there. The thing that is   easy to start is the thing you should not  do because everyone's going after that.
(25:10) Well, it goes back to my gym analogy of  if you're going to deadlift 300 pounds,   trained for 15 years, you should have already  had three startups and they should have worked and you've really built up the reps. And I  think that if I was going to start Instacart, that's different than someone who's 20  starting it. Not that I would be good at it,   but at least I would know what I'm getting into.
(25:32) Let's talk about something that is this endless  debate on Twitter. Maybe it's a false dichotomy   between lifestyle businesses, bootstrap  businesses, this idea of not raising money, just making revenue, living off the revenue versus  venture-backed venture scale companies feels like you're very good at the first bucket and a lot  of people, this is their dream, "I'm just going to start something. I'll start a lifestyle  business, make a few million a year, never   raise money. Who needs VCs? VCs suck." Advice for  deciding which route to go with an idea you have.
(26:04) Well, I think it's just not true that a  "lifestyle" or bootstrap business can't get huge. I mean, we bootstrapped the entire business and  now across all of our companies we do almost $300 million in revenue. And the whole time I had been  focusing on taking, starting small businesses, small ideas, simple ideas, often buying small  companies and watching them grow really big.
(26:40) And I think that the only difference between what  we do and what a venture capitalist does is the level of tolerance of burning money on fire.  And I just haven't seen that If we choose the right businesses that aren't in incredibly  competitive markets or where they have some sort of moat.
(27:04) So what I mean by that is a great  brand or a network effect like a social network or something like that, I don't feel we're holding  them back by not letting them light up a bunch of money on fire because these things naturally grow. The numbers, I mean they're like balloons, they just go as long as we don't mess them up too much.  So I think the decision ultimately comes down to how hairy do you want your big, hairy, audacious  goal to be? And if your big, hairy, audacious goal is I'm going to start the next satellite business  that creates some sort of crazy technological revolution. Yes, you're going to have to raise  a venture capital unless you're already a   billionaire or something like that. But if you're  wanting to just tinker and solve a problem that
(27:46) you think is not going to be hyper-competitive, so  for example, that form-filling thing or software that just does a narrow thing that doesn't require  20, 30, 40 million to be lit on fire before it can make money, then you can have a wonderful life  and build a wonderful company that can scale into the hundreds of millions of dollars if you  play your cards right without ever raising money.
(28:15) Per the story you shared with Flow where you're  competing against a venture-funded company, if there is a venture-backed company  in this space, is the advice you're not going to win competing with them most  likely and try to do something else. Well, look at Things. Do you know Things? I have used Things I do too. It's awesome. Things still exist and  Things has existed for 20 years.
(28:35) It's run by I believe one or two or three people. It's not  a big team, certainly under 10 people, it was, I believe, bootstrapped and it's just consistently  delivered an exceptional product and built up a loyal following. They have their 10,000 true  fans who use it, and that's enough for them, but they don't do a lot. They've been very  intentional. They don't do any AI stuff,   they don't have an API. They're not on  Android.
(29:07) They're very focused and I think they have succeeded. But the question is, what  would an MBA or a business professor say about their success? If the measure of success is  did they maximize taking as much market share as possible and grow to be as big as possible,  then the answer is no because Asana and other people have built multi-billion dollar companies.
(29:32) If the goal is the founder has an incredible life, probably has three houses, flies all over the  place, does whatever he wants, has recurring revenue and he gets to work with headphones  on building beautiful piece of software that   people love, then I think he's won. I am generally  much more in the camp of the Things guy has won, not Dustin Moskovitz. Dustin Moskovitz is just  playing a different game than the Things guy.
(29:55) And if Dustin Moskovitz was running Things, he'd be  miserable and if the Things guy was running Asana, he would probably kill himself because he  wants to put his headphones on and build. I wonder why nobody has come. It feels  like just with lifestyle businesses, if it's working and you would think  somebody would come in with more money   and more funding and just eat their  lunch, is the key that the market is too small for a VC to ever be excited and  so that's why no one's coming for them? I think so. I mean, I think Things probably, I'm  just guessing, I don't know any of the numbers
(30:26) or whatever, but I would assume it makes  between five and $25 million of revenue, which to a VC is not even worth considering  a VC. For a VC to invest in your business, you need to have a story where it's worth  300 million to a billion dollars or more. So it's just not that exciting to them, which  again, going back to fish where the fish are, you don't want to be fighting against the  commercial fishermen. If you just want to   have a little business where you get enough fish  to feed your family and your village or whatever,
(31:01) find the other fishing hole.  Don't go where the trawlers are. This is great advice if you're trying to start a  non venture backed company is find. This metaphor just keeps working for us, which is fish where the  fish are, but not where the professional fishermen   are.
(31:18) Also, ideally not where there's just a ton  of, I don't know, fishermen from all over the place. I don't know, solopreneur fishermen. Okay.  Coming back to just starting a successful company, what would you say are the keys to an amazing  business model, an amazing business broadly, what should you be thinking about and looking  into? I know you spent a lot of time thinking   about this.
(31:39) When you're buying companies, what  are a few bullet points that you want to focus on? What I do for a living is basically buy  businesses. So in my early career I started one business, then I started about 10 more, and then  I realized starting businesses was extremely hard and I was doing pretty well. I was making quite  a bit of money and I'd sold one of my businesses and I was looking out at the next 10 years and  really asking myself what do I want my career to be and am I happy doing what I'm doing? And the  answer was no.
(32:09) I did not starting businesses and experiencing that failure rate, it was incredibly  stressful. And so I picked up a book about investing and I got lucky. The first one I bought  was about Warren Buffett, and for those that don't know, Warren Buffett is he's counter to every  VC kind of hustle culture thing you might hear because he basically just sits quietly in a room  and reads all day, despite owning 260 different businesses and being one of the 10 wealthiest  people in the world, his life is incredibly calm.
(32:44) He only does what he wants and he spends most of  his time quietly reading and once or twice a year, he makes a big decision to buy a business. And so  when I read about Warren Buffett, I just thought,   wow, I'm a sucker. I'm running around like a crazy  person trying to run all these businesses.
(33:00) Why am I not just buying businesses and letting  them run? And so when I'm buying a business, what I'm really looking for is  something that I can't mess up. Right now, obviously we buy a business, we try  not to mess it up, and we're very intentional,   actually very odd in that when Tiny buys a  company, we generally just leave them alone.
(33:20) If they already have management in place, we say  nothing changes, no one should know that we've even bought them. Obviously people know that,  but really there should be no change whatsoever. The only change that we generally make is  if the founder wants to leave the business,   then we'll bring in a CEO to run the company, and  that's probably the most important decision that we make.
(33:49) So I'm looking for a business though  where it is so good that it's hard to mess up, and most businesses are very easy to mess up.  One person leaves and the whole business falls apart because it's held together with dental  floss and duct tape. And so I'm really looking for what Warren Buffett would call a moat. So  a moat is basically a brand, so that could be a brand like Coca-Cola, Tylenol, Advil, something  like that.
(34:14) So something that gives you pricing power where you can consistently charge and you  have loyal customers or where we usually find a lot of opportunity is in network effects. So typically we're looking for a community   that's gotten so big where people don't want to  go elsewhere. So for example, we own Letterboxd, which is the largest social network for film  lovers.
(34:39) And the question is, if someone else wants to compete with us, why would someone go  to their social network that has a small number of users when all their friends are already on  Letterboxd? And so you see that same kind of thing with Instagram or Facebook or similar. And so  we're looking for businesses like that, something   that has staying power that is hard to compete  with and that we can hold over the very long term.
(35:01) It was interesting when Elon bought X,  how that was such a test of the power   of network effects. If you think about  it, back then he changed the name. So the brand completely changed the team.  80% of the people left. What was left, it was the network and the network effect  and the simplest way.
(35:23) Just for folks that   aren't super familiar with network effects, the  way I think about it is just network effect is where every additional user that joins the  network becomes more useful for everybody. There's another moat, which is high switching  costs, which I don't really like because it's not really very consumer friendly, but an example  might be Salesforce where no one wants to, they've spent years and they've done all the  implementation and trained everyone on it,   and it's kind of the standard, everybody  hates it, but ripping it out and switching is such a pain in the that they just won't  bother. That's another form of sometimes a
(35:52) great business, but again, that's more  depressing. I don't love that one. What's the last company you guys bought? We bought Serato. Serato is the largest DJ  software company in the world. So if you ever see a DJ playing and they've got a laptop  in front of them, probably using Serato to DJ. Wow, that is super cool. What I'd  like a funding to be walking into.
(36:18) I used to DJ so I was aware of them and it  was one of those moments we talked about earlier where I understood the business. They  had a really interesting mode of this huge passionate user base and deep hardware  integration. And I also love DJing and I was able to analyze the business and luck  comes to the prepared mind and it worked out. This is another great callback to your  excellent piece of advice of just ideally   what you're working on, whether you're  starting the company or buying the company, is you have a unique unfair advantage or some kind  of unique background that connects to that idea.
(36:48) Let me ask you one more question before we shift  a whole different topic, hint, hint, AI. Something that I know you talk a lot about, something you  believe strongly and something you deal with a   lot is when you buy companies is just as people  and the challenges of management and hiring and people.
(37:11) So what have you learned about just how to  successfully, I don't know, find amazing people, keep amazing people, help people be more  successful within the companies that you operate? My business partner, Chris, has a really great  quote. He says, "There are no problems. There is only people problems." And so we've found that  you could tell us that there's a disaster that one of our business units is about to fail or  something like that.
(37:32) As long as I'm surrounded by really great smart people, I feel fine. But  when we have a bad actor, we have a psycho or a narcissist or a really horrible, difficult person  we're dealing with, all bets are off and life   becomes incredibly stressful. So I'd say I spend  probably 20% of my time just trying to make sure we're filtering people very carefully and ensuring  we're working with people that we enjoy.
(37:58) And I could talk about it in the AI, I've done some  really interesting stuff to help me screen and   identify difficult people, but I've just found  that the biggest mistake I made in my early career was I would hire people because I liked them on  gut and I would think that I could change them. So it's kind of like in romantic relationships,  it never works out well.
(38:18) If you get married to someone and you go, "Okay, well this is  going to be a great relationship as long   as I can convert them to Christianity or I can  change their parents or their personality," or whatever it is. And so I've just found that I've  never been able to change someone. You can never mentor someone out into being a good employee.
(38:41) And  the kind of heuristic I have is if I ever think, should I fire this person even once, I should  fire them immediately. It has always been a signal, at least for me, that when someone's a  superstar, I can't imagine firing them. I think it's impossible, I'd be lost without them. But when I repeatedly start going, "Man, is this person going to work out?" Almost always  within six to 12 months, it doesn't work out.
(39:05) And so I really try and be really direct about stuff  like that and just cut when it's not working. So I'd say the lesson really is hire for what  you need. Don't hire just for potential, which is counter to what a lot of people say. A  lot of people say hire for potential and coach   them into being whatever you need. I just haven't  found that.
(39:26) I think you need to hire someone who already is fully formed and can do what you  need. But again, everyone does it differently. This is big advice. Powerful advice. Is  this true not just for the CEOs of the companies you run? As for folks  under lower down the ladder too? Well, for CEOs, one of the most interesting things  we've observed is, so I'll give you an example.
(39:45) We had a CEO come in and we were interviewing  them and in my opinion, the business needed to grow via organic marketing. In his opinion, it was  enterprise sales. And so when we're interviewing him, he keeps going back to his experience  building with enterprise sales. And there's   this great saying, "To a man with a hammer,  everything looks like a nail.
(40:10) " And so for him, he's looking at this business and going enterprise  sales, that's the way to grow. I hire him, but I say, "Look, if we're going to hire you, we need  you to do the organic marketing." Lo and behold,   of course, he goes off and he does the enterprise  sales thing. And I've just found that hiring CEOs is like they're an elephant and you're the rider.
(40:34) They're going to go wherever it is that they want to go. And so listening incredibly carefully to  people's words and their experiences, because generally people will do the thing they tell you.  So what I look for when I interview a CEO now, I want to be nodding along. I want to go, that's  exactly what I would do or that's way smarter than   my idea.
(40:58) And then I just leave them alone because  I've found that anytime I try and pull them in a certain direction or coach them or whatever,  it just doesn't work. And again, this could   be my problem. I might be the world's worst coach  and mentor, but for me, that's been what's true. I imagine there's also an element of  they won't love the job if they want   to be doing say enterprise sales, and then they're creating viral TikTok videos all day and  tweeting. They're like, "What the hell?" Well, I've also found people will shoot  themselves in the foot.
(41:21) If I tell them an idea in a board meeting and I say, "I really need  you to try this," it never works because usually   they sandbag it, right? They don't really put  their heart into it or they're just placating me and they don't want it to work. They want their  idea to work. And so I've learned not to do that. This episode is brought to you by Miro. Every  day new headlines are scaring us about all the   ways that AI is coming for our jobs, creating  a lot of anxiety and fear.
(41:47) But a recent survey for Miro tells a different story. 76% of people  believe that AI can benefit their role, but over 50% of people struggle to know when to use it.  Enter Miro's innovation Workspace, an intelligent platform that brings people and AI together in a  shared space to get great work done. Miro has been empowering teams to transform bold ideas into  the next big thing for over a decade.
(42:10) Today, they're at the forefront of bringing products  to market even faster by unleashing the combined   power of AI and human potential. Teams can work  with Miro AI to turn unstructured data like sticky notes or screenshots into usable diagrams, product  briefs, data tables, and prototypes in minutes. You don't have to be an AI master or to toggle  yet another tool. The work you're already doing   in Miro's canvas is the prompt. Help your teams  get great work done with Miro.
(42:37) Check it out at Miro.com to find out how. That's M-I-R-O.com. Let's talk about AI. It's so funny to watch you on Twitter. Clearly you've become AI obsessed,  AIphile. I don't know what the term is, and I   love that you're not just the kind of person that  just talks about it and tweets pontifications, you're clearly using it in every facet of  your job and looking for more ways to use AI. So I have a lot of questions here because  I think this is where more and more founders   are going to be where you are today. First  of all, just what's in your AI stack these
(43:06) days? What are the tools you find yourself  coming back to most or finding most useful? So the primary tool I use is a tool called  Lindy, Lindy.ai. And what it basically lets you do is build workflows and agents. So it's very  simple. You might say, "When I get an email in the Gmail API, I want to add an AI agent that  reads the email and labels it based on that.
(43:32) " Or you can build a crazy Rube Goldberg machine  that sends different emails all sorts of different places. So for example, when I get an email and  it's related to my kid's school, one of the big problems I have is that my kid's school sends  so many emails, the field trip is on Thursday and you need to bring the following things. And  meanwhile, there's parent teacher interviews and   all these things get added to my calendar.
(43:59) The  AI agent automatically takes that, puts it onto my calendar, makes sure there's notes for all the  things I need to prepare and stuff. So I have just basically tried to take every single thing a human  could do in my inbox and automate it with Lindy. And you're sitting there in Lindy  doing this yourself or do you have   people that you've trained to help  build these sorts of things for you? No, I do it myself. Okay.
(44:23) How many agents, if that's the term you  want to use, do you have running for you roughly? Oh man, I think I probably have not a  crazy amount within... Let's see. Well, each workflow probably has four or five agents.  So for example, my inbox has four or five agents, and then I have a whole bunch of  other, I've got a calendar agent,   I've got a meeting agent, I've got an email  agent, I've got a scheduler, I've got a CRM, and basically they're all different workflows.  So the whole bunch of agents within. Okay, and you're not an investor in Lindy,  right? You're just a fan, super user.
(44:54) No, no. Fun fact. I actually am. So this is great. Oh, no way. That's awesome. Tiny angel investor. So just  want to put that out there, which I love that this came up organically. What  are a couple other really interesting use cases, workflows you've built that  people might be inspired by it? Let me go through the email one a little more.
(45:15) So  basically emails come in and the first agent says, does Andrew even need to see this? Let's say  you're in a thread and you've already chimed in and everyone's just saying, cool, that works.  I don't need to see that. So just archives that never. So that immediately reduces my email by  about 20%. Then it decides is this something that is time sensitive? Is this something that needs  to be dealt with in the next 24 hours? And if so, it labels it in a special 24-hour label.
(45:46) So when I  go in my email, one of the biggest stresses I find is I go, "Shit, there's 200 emails in here, and  if I don't go through all of them, I don't know   if there's an emergency burning somewhere." So  now I do. Then it takes any email or every single email and it decides, is this a simple decision? So for example, let's say you email me and you say, "Hey, do you want to get lunch?" So it emails  me privately and it says, "Hey, Lenny wants to get lunch. Do you want to say yes? Do you want to  say yes, but in a few months? Do you want to say   no? How do you want to say no?" And it gives me  multiple choice. I just can say four, number four,
(46:20) and then it'll email you as me and it'll write out  a nice thoughtful email or whatever. So stuff like that is so freaking cool and helpful. And I'd  say that it's replaced stuff that my full-time, I used to have a full-time assistant just working  on email that's all completely automated and there's all sorts of other cool stuff I'm doing  there.
(46:45) Other agents, I'm working on one right now to manage my calendar, which is really complex. I  do a lot of scheduling and a lot of rules. I have another really simple one. It adds emojis to  every single calendar event. So when I look at my calendar, I've got beautiful little emojis  for every single calendar event. Very silly.
(47:03) What are the emojis? Do they represent something? Yeah, so if I write weightlifting, it'll just do a  guy weightlifting or whatever. I've got one that, this one's cool. Basically what it does, every  time I email someone, it looks them up online, it figures out what city they live in.  It checks my CRM and air table. It says,   "Okay, I don't know where do you live.
(47:21) " In Marin, in the Bay Area. Okay. So it'd say, "Okay, you email Lenny,  he lives in Marin." It puts in the database you live in Marin. And then next time I travel  to the Bay Area, the agent will see two weeks before and it'll say, "I saw you're going  to San Francisco. Here's all the people in   San Francisco you should try and have coffee  with.
(47:43) " Right? So just all these things that- I would pay for that. Yeah, yeah, exactly. All these things  that I've dreamed of having my assistant   do that my assistant could just never  consistently do because she gets distracted, we're doing an event, there's  something going on. It's like   having the world's most reliable employee  who costs $200 a month and works 24/7.
(48:03) Okay, I want to follow that  thread, but first of all,   what other tools do you have? Lindy's  one, what else do you find really useful? So the other one is Replit. So Replit is  basically a vibe coding platform. You can literally go into it and say, "I want to make a  website for my sound software business.
(48:20) Here's a big document with a bunch of details,"  and it'll go and design a pretty impressive   website. But then you can also build web  apps now. So you can literally be like, "Yeah, build a Python web app that does XYZ,"  and the design is getting so good because it uses Claude 4. You can start saying, "Do it  in the style of Stripe.
(48:43) Think this through, rewrite all the copy and the tone of David  Ogilvy or Malcolm Gladwell," or whoever you like, and it does a really incredible job. So what I'm  finding is that things that previously would've frustrated me because I would've had to rely  on a team of five, I can just do entirely on my own when it comes to web projects and stuff  like that. So I'm having a blast with that.
(49:08) That's cool. So your team Replit versus  Lovable, Bolt, v0, is that roughly right? I mean, they're all great. I, just am the most  familiar with Replit, and it seems like it has the most functionality. It has a lot of  beef to it, whereas Lovable and Bolt seem a little more basic. You might have to deal with  deployment and stuff in a fiddlier way with them.
(49:30) Awesome. Okay. Anything else? Another one I love, so Limitless.  I don't know if you've seen this. I've got one of those. It's really cool. So clips to your shirt. I  actually just put it on my pocket so no one actually notices that I've got it on usually,  and I just have it on all day. And what it   does is it just records everything I do.
(49:52) And  then I can say, the other day I had a coffee with someone and I'm the kind of person  where I go a mile a minute and I say, "Oh,   I'll send you that and I'll do this," or whatever.  And so at the end of the day, I can just say, "Hey, what did I promise to people today?"  Or even better, everyone loves this use case, I have a fight with my girlfriend. And she says,  "You didn't say that.
(50:13) " And I'm able to say, "Well, actually you did." And you can query it as an LLM. So you can say you're a couples counselor. Look at this fight. What did each person need? How did  it start? What are the key lines? Where did it   change? How could you do this better? And so  honestly, from a relationship standpoint is probably where it's been the most useful. What's  cool too is that has an API.
(50:36) And so eventually, I haven't gotten there yet, but eventually  you'll be able to just have it record your   entire day and then automatically go  into your Todoist or whatever it is, and add to-do's or send emails  or whatever you need it to do. Wow, this is so fun. Or just per  your point with the relationship, ask it, "What could I have done better  today? Where did the day go downhill?" Yeah, I saw a guy on Twitter.  He said, "Every day I ask it,   'How could I have been a better dad?'"  And it'll be like, "Oh, your daughter
(51:04) tried to get your attention about this. You  should have paid attention," or whatever. And that was with the Limitless data. Oh,  man, I love that. With the relationship, you didn't go where I thought you were going to  go, where it's like, "Oh, but who actually said   it? What did you actually say?" I love that It  became much more wholesome of just like how could we have communicated better holistically? Okay. My  wife actually is like, "Don't wear this around.
(51:25) " You just got to wear it really covertly. Honestly,  I know it sounds kind of cheesy because you think like, "Oh, it'll tell her," right? But in reality,  usually you're both guilty in the fight. And my girlfriend has actually appreciated it because  there's been half the time it'll be like,   "Oh yeah, Zoe said this and it triggered  this," but a lot of the time it's me too.
(51:47) And I think I'm totally right. So I find  it actually really helpful. Other tools,   I mean otherwise, it's the basic stack of Claude  and ChatGPT and Gemini. And Gemini I use mostly for things that are really large. So for example,  all my medical records, I have trained on Gemini 2.5 just because the other ones can't really  go through it all.
(52:12) Claude, I use for writing very extensively. I find it's the best for that. And then ChatGPT for everything else. In terms of cool things I do, recently, I went through  my entire medicine cabinet and I just took   big photos that showed all the different medicines  and stuff. And now I can say something like, "Hey, I'm really tired and stressed out today.
(52:37) What  supplement should I take and what dose?" Or I'll say, "Remember all the medications I'm  taking, whenever I ask you a health question,   always remind me in the context of I might be  taking that medication." So a few times it's said, "Hey, you shouldn't take this other medication  because your genetics say you're not compatible with it and you're already taking this other  one." So stuff like that's been really helpful.
(53:01) For that use case, do you come back to  a specific conversation where you've given it that context, use a project or something- No, I say remember. I just say  remember these supplements, this is what I have in my medicine  cabinet or this is what I take every day. I see. So just tap into the memory of ChatGPT. Yeah. This is awesome. This makes me remember that.
(53:20) And  this connects to other points you've been making, I've started to use ChatGPT deep research  to prep for these conversations. I used to   have a researcher who did this for 400 bucks.  They did research on every guest that I had, four to 500 bucks, and gave me a whole doc,   "Here's everything about them.
(53:35) Here's their  background, here's questions you might want to ask." Deep research is better. Just  as of recently, especially with o3-pro. We were talking earlier about agents. I forgot  one of my favorite agents. So it looks on my calendar 30 minutes before I meet anyone. It  goes on to Perplexity and it does a deep dive on the person and it sends me, it goes into my  email and it gets the context of the meeting.   So often I book meetings three months out.
(54:00) So I usually look at my calendar and I go, "Who is this person? I don't even remember booking  this meeting." It'll text me before and it'll say,   "Here's who you are, here's your background,  here's the email context of why we're meeting." And it basically does all the research  for me. So I do the same thing. I also   use deep research and say, "If I'm meeting  them, you know everything about me.
(54:19) What are our commonalities and what are all  the fun things we should talk about?" Oh my God, I just want to plug and play  all these agents you've built to use them myself. Interestingly, I tried to create an  agent to do this research kickoff for me, but I don't think you can automate  ChatGPT deep research. Okay. Only Perplexity. Yeah.
(54:38) Okay. Perplexity is great too, but it's different.  Okay. So let me follow this thread I was going   down, which is around job displacement. I  know you think a lot about this. So with me, this researcher as a contractor I  was working with for a few months,   I no longer need them. Your assistant, you no longer need. Thoughts on just the impact  we're going to see on jobs as a result of AI.
(54:58) There's this great William Gibson quote, "The  future is already here, it's just not evenly   distributed." And I think that we are in the  Palm Treo phase. Do you remember the Palm Treo? No. So when I was, let's see, in 2007 I think,  was that when the iPhone came out? 2007? I know [inaudible 00:55:19]. Something like that.
(55:24) In 2007, there was this  device called the Palm Treo and it was like a little PDA with a stylus and it had a little  modem attachment. And so what you could do is you could get your email anywhere and that was  this shocking thing. It had a black and white or barely color screen and you could go in and  email people. And I remember walking through the mall buying shoes while answering business  emails and going, "This is the ultimate freedom.
(55:47) This is incredible." But the problem with that  device was it sucked. You had to use a stylus, it's not a good user experience. And then the  iPhone comes out and you're like, "Okay, this is what I was waiting for. This is the real thing.
(56:11) "  But I got a little preview with the Palm Treo   and so right now people like me who have the time  and skillset to nerd out and build the agents can do this. It's just not accessible to everybody. And I think that if it was accessible to everybody in terms of if you could just open ChatGPT  and say, "Hey, ChatGPT, I run a business, can you help me?" And it started asking you  questions and it said, "Oh well you do sales. Do you want me to set up a sales agent? Okay,  great, plug in your HubSpot API, now tell me a few things and then give me access to all your  data." And then before you know it, it's just a   digital employee that's on the other end of the  phone on ChatGPT just like in the movie, Her,
(56:46) that you can talk to and it can go do stuff.  I think that is kind of the iPhone moment that we're going to have in some time in the next five  years. And so I think that if AI doesn't progress, we will see some serious job displacement, like  what you already mentioned, the translators, the researchers, the admin, some assistant  jobs, depending on the kind of work they do, those jobs will definitely be massively affected.
(57:18) But I think pretty quick, all knowledge work jobs could be affected if the models scale in the  way that they say they're going to. I mean if you trust Sam Altman and Dario Amodei, I mean  Dario Amodei said by, "2027, our models will be smarter than all PhDs." So in any subject. That  is a staggering statement. And he's been a very conservative kind of almost fearful voice in the  AI world. Very cautious to say things like that.
(57:50) And so when he said that, I perked up and started  listening carefully. I don't know that that's how it's going to play out. He might be trying to  fundraise, we have to take it with a grain of   salt. But if that's true, then I think so many  knowledge work jobs are going to change massively. Okay, there's a lot here.
(58:17) I actually  had Mike on the podcast, their CPL,   Mike Krieger, and he pointed out that Dario,  every prediction he's had so far has been right over the past few years. And so there's a  reason to listen to his insights. Okay. Andrew, I know you're not going to have all the  answers for people. But let me ask you   kind of a two-part question for new grads and  for people currently in the workforce say like, I don't know, senior product manager advice on  what they should do, where should they focus? What skills will matter most? What jobs do you  think will last? What advice can you give us? Everybody 10 years ago, do you remember there   was this whole movement that we should  teach kids how to code? Remember that?
(58:50) Mh-hmm. Now teaching kids how to code is teaching kids  Basic and we have a GUI, right? You don't need to learn punch cards and basic programming or MS-DOS  because we all have keyboards and mice. We don't have to do that. And I feel like coding has really  gone away because of that.
(59:12) And I think now people are saying people need to learn how to prompt,  which I think is very true for the tool set right now. But I don't know how relevant that is in  the future because I think we're still, again, in the Palm Treo phase, I think that the AIs will  be very good at eliciting what the actual problem you're trying to solve is and reinterpreting  it in a way that it can optimize the query to get the right answer and solve the problem.
(59:41) And so I think the fundamental question is do all jobs just become a single prompt? For example,  does a CEO just grow the business while making the customers happy and turning a profit or something  like that? And it is able to actually be an omniscient presence that can run a whole company.  Now that's a big I'm someone building AI agents. I can barely get it to reliably do calendar  entries and that kind of stuff.
(1:00:07) So I don't think that's totally imminent, but I can certainly see a  world where that's coming. And so the question is, what is one to do when that reality is barreling  down at you? For people who have resources, I think there's a lot of things they can do in terms  of investment, they can invest in the companies   that'll benefit from this, companies that have a  lot of compute or energy or that sort of thing.
(1:00:29) So that's one thing, but that's not really the  question. The question is what do you do if you're   a smart 18-year-old or 19-year-old or whatever?  In my opinion, I think the best thing to do right now is to get incredibly good with these tools  and utilize them to build enough wealth that you can put the money into diversifying into  compute and energy.
(1:00:53) And I do think that while everybody thinks that all jobs will go away  in five years and robots will be everywhere, people generally overestimate things in the  short term and underestimate them in the long   term. So I think there's going to be this  long window where robotics is not anywhere near good enough to even do raking leaves  and stuff. And I think there's going to be   a ton of opportunity for people to just spin  up new businesses that never existed before.
(1:01:20) I was talking to some friends and we  were like, "What do you do in a world   of abundance where everything is really cheap  and companies operate almost autonomously?" I think there's a lot of weird skillsets  that we can't imagine right now. For example, just being funny, right? Being funny to hang out  with them.
(1:01:41) Think about OnlyFans, right? Right now, there's this whole idea of OnlyFans where  people are paying for comfort and connection, but in a romantic sense. Imagine people did that  day to day, they just love hanging out with funny people. So you have a funny guy who you chat with  or comes over to your house or whatever it is,   I'm making stuff up.
(1:02:01) Maybe it's a guy who  comes to play pickleball with you or something, I don't know. But I can imagine that in this  weird world, there's all these weird new jobs. So I think my take is things will either be  totally fine or they will be terrible and we'll all be dead. And I can't predict which those is  there. I don't think it's worth thinking about   all of us being dead.
(1:02:24) And so I think it's  really just lean into what are the tools, how do you stay cutting edge? How do  you build businesses and wealth in   this new world? But I am certainly  feeling a little bit like all of our brains could be defunct in the next 10  years. And that's kind of a scary thought. I'm picturing this dystopian world where  humans are just going around making jokes.
(1:02:44) Where does status come from in a world  of abundance? Where does status come   from? Maybe it is just like being  the best at hiking or something. Well, let me ask you this because this is  where it gets even more real is just with   your kids.
(1:02:59) What are you encouraging them  to learn? What are you encouraging them to get good at? Because this is the real problem a  lot of people are having right now is what will matter in the future. Obviously we don't know,  but what are you encouraging them to focus on? Well, I don't know. I mean there's so little,  they're five and eight. And so really what I'm focused on is making sure they're socialized  and they're polite, very basic.
(1:03:20) So are they comfortable talking to adults? I often send  my son, whenever he wants something at a cafe,   I always make him go up, ask nicely, pay himself,  all that kind of stuff. But at this point I feel like for the first 10 years of life, you're  just trying to make sure that they're not   traumatized or rude.
(1:03:41) And I think it's going  to be really interesting when they're 10 plus where they're really aware of this stuff and can  start building with these tools. And to be honest,   I just don't know. I think I just want them to  lean into whatever they're passionate about and go from there. But I have no idea.
(1:03:57) And to be honest,  it's not something I'm worrying about because it's too hard to worry about. I don't know, it's too  multivariate. There's too many ways it can go. I feel like a core part of your message is just  get good with these tools. There's this classic   quote, "AI won't replace you, it'll be somebody  very good using AI is going to replace you," at least for a while. And so it feels like a big  part of this is just use these things.
(1:04:23) Before   we started recording, we were having a mic issue  and I love you just went straight to ChatGPT, "ChatGPT, how do I connect this mic to  reverse side and make it work?" And I   feel like that's just a habit to build  is just like go to ChatGPT whenever you have a problem like that. I've been  doing that a lot.
(1:04:37) I was connecting a   subwoofer from what wire do I need to buy  to connect the subwoofer to the receiver? Well, the best part about ChatGPT is I used  to just get stuck and I have ADHD. So when I got stuck, let's say, what's an example?  I was redoing my home IT security. And what will happen is I'll be in the terminal  and I'll enter something and then something   goes wrong and it just keeps erroring. And  then I'm like, "Well, I'm out.
(1:04:58) " I have ADHD, I can't tolerate this. I just lose the thread  completely. And ChatGPT allows me to stay on track and go so much further and faster  than I ever could before. I love not being artificially stopped in your tracks and being  able to continue the flow state as a result. And also I noticed you were using voice  mode, which is what I've been using more   and more.
(1:05:23) And I feel like that's something a  lot of people sleep on is just the voice mode where you could talk to it and not have to sit  there and type your questions. Okay, so let me go in a different direction. You've been spending  a lot of time, you wrote a whole book about this,   of just your journey from, you call it Barista  to Billionaire where you started serving coffee, then you ended up being very successful, then  realize that didn't make you happy.
(1:05:44) You're quite unhappy with lots and lots of money. I think a lot  of people hear these stories, they see this stuff, they're like, "Yeah, okay, I sort of get it."  But they still do the same sorts of things. They   still assume they will be happy once they reach  that next goal, that next title, make certain dollar amount.
(1:06:10) What can you share? What have you  learned about just what it actually takes to be   happy that you think people are still not really  recognizing and still kind of are confused about? Do you remember when you're in your early 20s? I  remember when I was in my early 20s, I'd always be like, "I need to move away to Europe. I need  to go discover myself." And I'd be really anxious and stressed out and in an existential crisis  and then I'd fly to Europe to go backpacking or something and I'd still feel anxious and have my  existential crisis. It's just now I'm in Europe.
(1:06:35) And I think the reality is that whatever's  in your brain, whatever that anxiety loop is, doesn't go away just because you have a bigger  house or more money in the bank account or you're   in Bali or wherever it is, that's a chemical  reaction happening inside you that relates to your past and your DNA and all that stuff  that creates that soup.
(1:07:00) And for me, I've always been a very anxious person. I've always worried  about tomorrow, I rarely enjoy today, I'm always worrying about what could go wrong in health or  my businesses or with my family or whatever it is. And so when I started my business, all I thought  was I just don't want to work for someone else. If I can just wake up in the morning, do my own  thing, make 60K a year, then I'll be happy.
(1:07:25) So I got that and then it was once I make a  million dollars a year and I can buy a house, well, I got that and so on and so on and so on  all the way up to at one point being worth over a billion dollars, by the way, should redo the  title of the book from Barista to Billionaire,   now it's former billionaire because our stock  went down, but it didn't change anything.
(1:07:49) I'm still just as anxious as ever. I remember  a month ago, I had this day where I was sitting in the sauna and I was stressing about a  bunch of business problems and money things and I stopped myself and I was like, "Wait a minute." 10 years ago I had all the same thoughts and I was stressed out about similar kinds of problems and  our revenue was $20 million bucks or $15 million bucks. Now we're at almost 300 million and I'm  still stressed about the same things.
(1:08:24) And the saddest part was as I met more and more wealthier  people all the way up to multibillionaires, I realized they were all still comparing themselves  to their peers, competing over who has what and tracking stuff and still unhappy that anxiety  loop or depression was still in their brain. I remember I had this moment where I met this  guy who is a single-digit multibillionaire, and he goes, "Oh, Jeff Bezos, he's just  so fucking rich.
(1:08:58) " And I was like, "Wait, what do you mean you're worth $5 billion?  What can he do that you can't?" And he goes, "He can buy a super yacht." And it's like, "What?" You can look at that guy and you can say, "Oh, that's a wacko and all the other stuff." But the  reality is that we're all just comparing ourselves to our peers, whatever our peers have that we  don't feel hard done by.
(1:09:26) And I remember just thinking like, "Oh my God, how do I avoid becoming  this?" And that's the scary reality is no matter how lucky you are, I mean we're also fortunate.  For me, I live in Canada. I have every opportunity that I ever could have hoped for. And  there's people all over the world who   have nowhere near what I had growing up.
(1:09:50) And yet I felt hard done by because in my neighborhood I was the poor kid. I wasn't  poor globally. I wasn't even poor based on Canada. But all my friends had big screen  TVs and would go to Hawaii and I never   got to do any of that stuff. My parents were  stressed about money. So I feel hard done by. There's a phrase of friend's  financial advisor once shared,   "The Joneses are doing really well," and  that's hard to get over.
(1:10:15) So what changes did you make to just be happier? It's hard to  give up money. It's hard to give up more money you could make. It's hard to give up things that  money buys, I guess just so what did you change? Well, there's a few things. I mean, one was  reframing money to some degree to have it be something that wasn't just about feathering my  own nest and making it better.
(1:10:41) So for example, I was talking to a friend who's very wealthy and  I was saying all the feelings I described of have all this and yet I don't feel better. And he said,  "Well, what are you working for?" And I said, "Well, I guess I'm just working for the numbers  to get bigger and have more employees and that's nice. Our businesses do good things in the world  and help more people and stuff, but ultimately   it's just to pile up cash.
(1:11:12) " And he said, "I've  reframed it so that all my money, 90% of my money plus, goes into my philanthropic foundation and  then it gets given away to great causes. So when I think about working really hard or losing a  deal or an investment going well, the win isn't for me. The win is about doing something good." So I did that and that definitely helped. I also stopped spending as much money.
(1:11:36) I found that  the more stuff I had, the more houses I had, the more people that directly worked for me to  manage all that stuff, the less happy, I was owned by my stuff. And then I work really hard not  to have people... I try not to beat people over the head with it. I drive a normal car. I don't  bring people to my house until I know them really, really well. I meet them on neutral ground, I  meet them in a cafe. I dress like a schmo.
(1:12:04) I just try not to be a weird out of touch rich person.  But the biggest thing, which is a weird answer, is actually medicating myself. So basically  all my life I've been really anxious. And in 2020, I remember I was watching a  movie with my ex-wife and I was looping on, "I need to respond to this email. If I don't  respond to that email, this person's going   to be mad.
(1:12:36) " And then I realized that I was  watching a movie for the last 20 minutes and I didn't know anything about what was happening  in the movie. And then I was going, "Oh my God,   my wife is going to be mad at me. We're going  to get in a fight," and I'm projecting all this negative stuff. And in that moment I was  like, "Okay, maybe I do need to try an SSRI or something. This is not sustainable.
(1:12:55) " And so I  went to my doctor and I got prescribed with one, and I remember I was so scared of it  that I cut it into 10 pieces. I said,   "I'm just going to take a tiny little piece." And it took me six months after that to try any. But I started taking these pieces every day and I  just noticed that within about three or four days, it was like someone had turned down the volume  on the nasty voice inside of me doing all that.
(1:13:17) And for the first time in my life I felt  relief. No amount of money or success or attention or anything else had done what this  little tiny yellow pill could do for my mental state. And so now I've been on an SSRI for  four and a half years or something like that. And then I also started medicating my ADHD.
(1:13:41) And I would say that my brain went from Times Square to a quiet library. And I think a lot  of people are scared of that, but ultimately most people are acting out something that's  going on inside of them. There's a trauma or something medically going on and that's creating  this anxiety, this depression, this feeling and addressing that is so much more important than  trying to get praise from the external world.
(1:14:05) Wow, it's really powerful. I really am really  thankful you're sharing this. The advice most people hear, the general, I don't know,  culture is just like, "No, don't take meds.   Don't medicate yourself, I don't know, there's  all these downsides, all this risk. The pharma industry is trying to make money." I love this  other perspective of it. It's actually really   effective and actually makes a big difference  in your life and it may be the only solution.
(1:14:29) Well, it's funny because if you have a  headache, I don't think many people say,   "Oh, don't take Tylenol, you're benefiting  Johnson & Johnson, big pharma." They go, "Yeah, you take a Tylenol when you have a  headache.
(1:14:50) " Or a lot of people have seasonal   allergies and they take antihistamines and they  forget that histamine is not just in your nose, histamines in your brain and everywhere  in your body. And so when you take an   antihistamine or even a Tylenol, you're changing  your brain, you're changing the way you think, you're changing everything. And yet for some  reason, people are so scared, myself included, of taking an SSRI or similar medication to treat  something that is ruining their life.
(1:15:12) Anxiety was ruining my life. Every minute of every day I was  convinced that something terrible was about to happen. And even now medicated, I still have that  voice. It's not like that frankly beneficial voice that helps you grow your business or whatever  becomes unproductive at a certain point. And I think that it is wonderful to be able to  actually just get a little bit of solace from it.
(1:15:38) Well, for folks that, I mentioned many folks  listening, are like, "Oh man, I should really   explore this," what's the best way to explore  is to talk to your primary care physician? Yeah. I think talking to your doctor, and one  note I think a lot of people miss is when I was so scared of side effects because you hear about  all these horrific side effects of people lose   their libido and feel terrible and stuff.
(1:16:03) And so  I looked into it and a lot of the side effects are due to the way that people metabolize different  drugs. And so you can actually do your 23andMe or similar DNA test and you can find out  how you metabolize different SSRIs. And then I chose one that I could metabolize well.  And so I've had no side effects or problems. And I know every time I mention this, I get a  lot of people that say exactly what you say,   big pharma and all this other stuff.
(1:16:29) But  I just ask everyone listening, if you take a Tylenol or you take an antihistamine  or other medications, I don't know why something that can be so profoundly helpful  is something that people need to be afraid of. Amazing. Andrew, we've covered so much ground.  I love the spectrum of topics we've gone after. Is there anything else that you wanted to share?  Anything you wanted to double down on? Any last nugget you want to leave listeners with before  we get to our very exciting lightning round? One thing that was shocking for me was I was  doing... My doctor asked me to get a cognitive
(1:17:04) test, not because I had any problem, but he said,  "Look, it's really important to get a baseline in   your 30s because then as you age, we can make sure  that you're not getting Alzheimer's or dementia or whatever." So I go and I do this test with a  neurologist and she asked me a bunch of questions.
(1:17:19) It's, "Remember these 10 digits and say them  backwards and all this stuff." And finally when I get my test results back, she said, "Look,  your crystallized intelligence, your ability to remember stuff, long range is totally fine.  You're above average. Your short-term memory, your working memory is 10th percentile, very, very bad.  And you probably should get checked for ADHD.
(1:17:48) " And I was like, "Nope. There's no way. I don't  have ADHD. I'm organized. I've got to-do lists. I built a business. I wasn't the hyper kid in  school or anything like that." And she said,   "Well, look, just humor me. Just go get tested."  And so I went through this crazy process. You can go online and just quickly get tested  or whatever. But I went through this really,   really five-day process to get tested. And it  turned out that I do have it.
(1:18:18) And it's really interesting because if you take the normal  population of about 5% of people have ADHD, but about 30% of entrepreneurs have  ADHD. And many entrepreneurs I talk to, they love new things. They love jumping around  between a million different topics. They're an inch deep and a mile wide like I described.  And they describe themselves as unemployable.
(1:18:41) And frankly, I remember I was so skeptical  of ADHD that when my girlfriend told me, a friend of hers got diagnosed with it, I  sarcastically said, "Oh good, she better   take some meth," talking about stimulant drugs. Previously I was very, very skeptical of ADHD, but it is a real brain disorder if you  actually dig into it.
(1:19:03) It's a very real thing, very objective that people have poor executive  function. And for me, it's given me a lot more empathy for myself and my behaviors because  at work, if you have ADHD, you can delegate to other people and build systems to get around your  disability. But at home, that's where it really   plays out.
(1:19:25) And so for me, it's like my girlfriend  asked me to take the garbage out, and I say, "Of course." And then I forget three times in  a row. And she views that as you are being very hurtful and you're not caring for me. And it  causes all sorts of different problems at home. And so for me, not only did it make my work  life better because I was more focused, but it actually really helped me in my personal  life and it made me feel not broken.
(1:19:49) I always felt a bit broken. I couldn't put my finger on  why, but I just went, "I'm not good at doing   all the things everyone else seems to be able  to do no problem." And so I think especially because you have an entrepreneur audience, I  think a lot of entrepreneurs should consider   getting checked for ADHD.
(1:20:08) Just go and ChatGPT say,  "Ask me a of questions and tell me if you think I might have it." But I was very surprised,  and that's been really impactful for me too. Man, this garbage story is  very descriptive of my life,   and so I'm going to do this. There's this  implication that it's good to know this, right? It feels scary to feel like, "Oh,  shit I might get diagnosed with ADHD."   Your advice I imagine is it's better to  know because you could do stuff about it.
(1:20:33) Totally. I think it's like any medical thing,  right? It's like there's an Alzheimer's gene, right? And if you know you have it, there's a  lot you can do to slow the disease or prevent maybe getting it or whatever. It's always  better to know.
(1:20:55) Even if you don't medicate,   I think there's a lot of lifestyle interventions,  dietary interventions, and just things you can do to make your life easier, even if it's  just explaining to your partner, "Hey,   I need things to be explained to me this way  so that I can be effective in loving you." Beautiful. Okay. Well, Andrew, with that,   we've reached a very exciting lightning round.  I've got five questions for you.
(1:21:11) Are you ready? Let's go. First question, what are two  or three books that you find   yourself recommending most to other people? If I could only recommend two, I would  recommend The Laws of Human Nature by   Robert Greene. It's basically like a compendium  of every single type of personality disorder or psychological effect.
(1:21:33) How do people work  their minds to make bad decisions? What is a narcissist? What is a psychopath? All that  kind of stuff. And Robert Greene does such   a great job of telling all that in a really  engaging way by using storytelling. So every single chapter has a story about someone with  that personality disorder. So I love that book. The other one that I love is a book by  Felix Dennis called How to Get Rich.
(1:21:58) It's   hilarious because the whole book starts  out saying, "I got rich and I regret it, and I wish that I'd become a poet." And instead he  went off and he built this huge publishing empire, became a poet in his sixties, and then he  died of throat cancer. So it's his story,   he basically says, "I'll tell you how to build  the business even though you probably shouldn't." And I read that when I was in my 20s, and I found  it so exciting and inspiring, and then I recently reread it and I was like, "Oh my God, why didn't I  listen to this guy?" Everything he said came true. Next question, do you have a favorite recent  movie or TV show that you have really enjoyed?
(1:22:28) I love Challengers. I don't know if  you've seen it, but it's amazingly acted,   amazing cinematography. It's like this amazing  romantic triad tennis movie. Really, really good. Do you have a favorite product  you recently discovered that you   really love? You already mentioned  Limitless.
(1:22:45) Is there anything else? I'm one of those people who really believes in  the idea of a robot vacuum. I've probably spent $10,000 on different robot vacuums over the  years, probably five or six of them. And I   finally got one that actually works. It's called  the Matic Vacuum, M-A-T-I-C. And basically, I think it's like former Google Engineers  basically built like a mini Waymo car.
(1:23:12) So it has machine vision and it will avoid  absolutely everything and never get tangled, and it can mop your floors and vacuum.  I've been super impressed with it so far. I've got one of these myself. Completely agree. My  wife loves it, which I did not expect. She's like, "Turn that Matic on." It's very delightful. It has  his little personality and it just does a really   good job. And I'm not an investor, and that's  awesome. I'm a huge fan. Okay, next question.
(1:23:33) Do you have a favorite life motto that you often  come back to find useful in work or in life? Yeah. My favorite is a quote by Jerzy Gregorek.  It's, "Easy choices, hard life. Hard choices, easy life." And I often find that anytime that  I'm making the easy choice, my life gets hard. And when I make the hard choice to shut down the  business, fire the person, say bye to a project that I was excited about, it's almost always the  right choice and it makes my life a lot easier.
(1:24:08) Final question. Okay, so let me know if this  is true. I found that when you were a teenager, when you're running Macteens, you interviewed  Steve Jobs at a conference. Is that true? So when I was a teenager, I had a tech news  website and I emailed the Apple PR people and I said, "Hey, could I interview Steve Jobs?"  And they kind of laughed at me. They're like,   "Yeah, there's no way.
(1:24:34) You're not  going to interview Steve Jobs, but hey, why don't you go to this tour of  the Apple Store?" So this is no other Apple Store has existed. This is in New  York at Macworld back in 2004, no, 2003, something like that. And so I was really excited  about that. So I show up to go do this tour and there's 30 other journalists there, and I'm first  in line, and this big black SUV pulls up and this man gets out and the man is wearing gray New  Balance, little circular John Lennon glasses, the mock turtleneck. And my brain finally puts  it together and I realize it's Steve Jobs.  And he walks directly up to me because I'm first  in line and he shakes my hand and he says, "Hi,
(1:25:17) I'm Steve." And I'm like a quivering mess. I'm 17  years old. He is my hero. He's my Jesus. And so I was just so high on adrenaline that I just started  asking him questions. As soon as we walked in, I basically just stayed at his side, and  I just picked his brain about all sorts of   different stuff. So it's not like I got to ask  him questions about life.
(1:25:41) I was asking him about the new 17-inch iMac or whatever. But it was  a pretty cool experience to get to meet him. That is awesome. There's such a funny  thread recently with guests. Everyone,   there's like a Steve Jobs connection for the  past couple months of guests. That's an awesome story. It just shows that it wasn't luck. You  made this happen. You got there first in line. The lesson for me was ask big and maybe  you'll get something great.
(1:26:05) Ask for amazing, you'll get something great, which I did. Yes, door in the face technique,  I think people call it. Yeah. Andrew, this was incredible.  Two final questions. Where can   folks find you if they want to reach  out? I know you're buying companies, investing in companies. Just talk about what  you're looking for there in case people are like,   "Oh, this is me.
(1:26:26) " And then finally,  how can listeners be useful to you? Yeah, my business is Tiny, T-I-N-Y.com, and we buy  businesses. Basically, when we sold the business, we hated it. We went through this process where  we talked to all these douchebags that ran private equity firms that would show up in our office  in suits and use a bunch of words we didn't   understand.
(1:26:55) And we ended up going, "Man, why can't  we just sell to somebody who's like us?" And so we basically started Tiny to become the buyer we  wish we could have sold to. And we're looking for businesses that have some of the qualities  I mentioned before. So really high quality   businesses that do something positive in the  world and have happy customers, happy employees, and some kind of competitive advantage that  will make them continue into the future.
(1:27:16) So we own AeroPress, Letterboxd, Dribble, which is a  design social network, whole bunch of businesses like that. So if somebody is thinking about  selling their business, definitely get in touch. Amazing. Okay.
(1:27:38) And then how can listeners  be useful to you if it's not just that? I just would say if there's anyone really  interesting coming to Victoria, Canada, which is where I live, send me an email  and would love to have coffee with you. The reason I love going on podcasts is because  I get to meet so many interesting people,   both randomly. If I'm in a cafe and someone  sees me and says hi, I often make a lot of friends that way, or if someone comes to  my hometown. So yeah, just get in touch. Amazing. Andrew, thank you so much for being here.
(1:27:59) Yeah, thanks, dude. That was fun. Bye, everyone. Thank you so much for listening. If you found this valuable, you  can subscribe to the show on Apple Podcasts,   Spotify, or your favorite podcast app.  Also, please consider giving us a rating or leaving a review as that really helps other  listeners find the podcast. You can find all   past episodes or learn more about the show at  lennyspodcast.com. See you in the next episode.