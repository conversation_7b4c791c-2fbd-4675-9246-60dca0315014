# 数据清洗与备份策略 - 数据质量保证和安全备份机制

## 📋 学习目标

完成本模块学习后，您将能够：
- 建立系统化的数据清洗流程和质量控制体系
- 设计多层次的数据备份和恢复策略
- 实施自动化的数据质量监控和异常处理
- 在电商AI场景中确保数据的完整性和可用性
- 构建数据生命周期管理和合规保护机制

## 🎯 理论基础

### 1. 数据清洗的系统方法论

基于您的information analysis.txt中"数据清洗，主要包括：去重、删除、重命名、异常值&缺失值标记"的核心要点，数据清洗是确保信息质量的基础工程。

#### 1.1 数据质量问题分类
```mermaid
graph TD
    A[数据质量问题] --> B[完整性问题]
    A --> C[准确性问题]
    A --> D[一致性问题]
    A --> E[时效性问题]
    
    B --> B1[缺失值]
    B --> B2[不完整记录]
    B --> B3[字段缺失]
    
    C --> C1[错误值]
    C --> C2[异常值]
    C --> C3[格式错误]
    
    D --> D1[重复记录]
    D --> D2[格式不一致]
    D --> D3[编码不统一]
    
    E --> E1[过期数据]
    E --> E2[更新延迟]
    E --> E3[时间戳错误]
```

#### 1.2 数据清洗流程框架
```python
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import re
import logging

class DataCleaningPipeline:
    def __init__(self):
        self.cleaning_stages = {
            'initial_assessment': {
                'description': '初始数据评估',
                'methods': ['data_profiling', 'quality_assessment', 'schema_validation'],
                'priority': 1
            },
            'structural_cleaning': {
                'description': '结构性清洗',
                'methods': ['duplicate_removal', 'schema_standardization', 'encoding_normalization'],
                'priority': 2
            },
            'content_cleaning': {
                'description': '内容清洗',
                'methods': ['missing_value_handling', 'outlier_detection', 'format_standardization'],
                'priority': 3
            },
            'validation_and_verification': {
                'description': '验证和核实',
                'methods': ['business_rule_validation', 'cross_reference_check', 'quality_metrics_calculation'],
                'priority': 4
            },
            'final_optimization': {
                'description': '最终优化',
                'methods': ['performance_optimization', 'metadata_enrichment', 'documentation_update'],
                'priority': 5
            }
        }
        
        self.quality_metrics = {
            'completeness': 0,
            'accuracy': 0,
            'consistency': 0,
            'timeliness': 0,
            'validity': 0
        }
    
    def execute_cleaning_pipeline(self, raw_data, cleaning_config=None):
        """执行数据清洗流水线"""
        cleaning_result = {
            'original_data_summary': self._summarize_data(raw_data),
            'cleaning_stages_results': {},
            'final_data_summary': {},
            'quality_improvement': {},
            'cleaning_log': []
        }
        
        current_data = raw_data.copy()
        
        # 按优先级执行清洗阶段
        for stage_name, stage_config in sorted(
            self.cleaning_stages.items(), 
            key=lambda x: x[1]['priority']
        ):
            stage_result = self._execute_cleaning_stage(
                current_data, stage_name, stage_config, cleaning_config
            )
            
            cleaning_result['cleaning_stages_results'][stage_name] = stage_result
            current_data = stage_result['cleaned_data']
            cleaning_result['cleaning_log'].extend(stage_result['operations_log'])
        
        # 最终数据摘要
        cleaning_result['final_data_summary'] = self._summarize_data(current_data)
        
        # 质量改进评估
        cleaning_result['quality_improvement'] = self._assess_quality_improvement(
            cleaning_result['original_data_summary'],
            cleaning_result['final_data_summary']
        )
        
        return current_data, cleaning_result
    
    def _execute_cleaning_stage(self, data, stage_name, stage_config, cleaning_config):
        """执行单个清洗阶段"""
        stage_result = {
            'stage_name': stage_name,
            'input_data_shape': data.shape,
            'operations_performed': [],
            'operations_log': [],
            'cleaned_data': data.copy(),
            'stage_metrics': {}
        }
        
        for method in stage_config['methods']:
            try:
                method_result = self._apply_cleaning_method(
                    stage_result['cleaned_data'], method, cleaning_config
                )
                
                stage_result['cleaned_data'] = method_result['cleaned_data']
                stage_result['operations_performed'].append(method)
                stage_result['operations_log'].extend(method_result['log_entries'])
                stage_result['stage_metrics'][method] = method_result['metrics']
                
            except Exception as e:
                error_log = {
                    'timestamp': datetime.now().isoformat(),
                    'stage': stage_name,
                    'method': method,
                    'error': str(e),
                    'severity': 'error'
                }
                stage_result['operations_log'].append(error_log)
        
        stage_result['output_data_shape'] = stage_result['cleaned_data'].shape
        
        return stage_result
    
    def _apply_cleaning_method(self, data, method, config):
        """应用具体的清洗方法"""
        cleaning_methods = {
            'duplicate_removal': self._remove_duplicates,
            'missing_value_handling': self._handle_missing_values,
            'outlier_detection': self._detect_and_handle_outliers,
            'format_standardization': self._standardize_formats,
            'encoding_normalization': self._normalize_encoding,
            'business_rule_validation': self._validate_business_rules
        }
        
        if method in cleaning_methods:
            return cleaning_methods[method](data, config)
        else:
            return {
                'cleaned_data': data,
                'log_entries': [{'method': method, 'status': 'not_implemented'}],
                'metrics': {}
            }
    
    def _remove_duplicates(self, data, config):
        """去除重复数据"""
        original_count = len(data)
        
        # 基于所有列的完全重复
        data_deduplicated = data.drop_duplicates()
        
        # 基于关键字段的重复（如果配置了）
        if config and 'duplicate_key_columns' in config:
            key_columns = config['duplicate_key_columns']
            if all(col in data.columns for col in key_columns):
                data_deduplicated = data_deduplicated.drop_duplicates(subset=key_columns)
        
        duplicates_removed = original_count - len(data_deduplicated)
        
        return {
            'cleaned_data': data_deduplicated,
            'log_entries': [{
                'method': 'duplicate_removal',
                'original_count': original_count,
                'duplicates_removed': duplicates_removed,
                'final_count': len(data_deduplicated),
                'timestamp': datetime.now().isoformat()
            }],
            'metrics': {
                'duplicate_rate': duplicates_removed / original_count if original_count > 0 else 0,
                'records_retained': len(data_deduplicated)
            }
        }
    
    def _handle_missing_values(self, data, config):
        """处理缺失值"""
        missing_summary = data.isnull().sum()
        total_cells = data.shape[0] * data.shape[1]
        total_missing = missing_summary.sum()
        
        cleaned_data = data.copy()
        operations_log = []
        
        for column in data.columns:
            missing_count = missing_summary[column]
            missing_rate = missing_count / len(data)
            
            if missing_count > 0:
                # 根据缺失率和数据类型选择处理策略
                if missing_rate > 0.5:
                    # 缺失率过高，考虑删除列
                    if config and config.get('drop_high_missing_columns', False):
                        cleaned_data = cleaned_data.drop(columns=[column])
                        operations_log.append({
                            'operation': 'column_dropped',
                            'column': column,
                            'missing_rate': missing_rate,
                            'reason': 'high_missing_rate'
                        })
                elif data[column].dtype in ['int64', 'float64']:
                    # 数值型数据用均值或中位数填充
                    fill_value = data[column].median()
                    cleaned_data[column] = cleaned_data[column].fillna(fill_value)
                    operations_log.append({
                        'operation': 'missing_filled',
                        'column': column,
                        'method': 'median',
                        'fill_value': fill_value,
                        'missing_count': missing_count
                    })
                else:
                    # 分类数据用众数填充
                    fill_value = data[column].mode().iloc[0] if not data[column].mode().empty else 'Unknown'
                    cleaned_data[column] = cleaned_data[column].fillna(fill_value)
                    operations_log.append({
                        'operation': 'missing_filled',
                        'column': column,
                        'method': 'mode',
                        'fill_value': fill_value,
                        'missing_count': missing_count
                    })
        
        return {
            'cleaned_data': cleaned_data,
            'log_entries': operations_log,
            'metrics': {
                'original_missing_rate': total_missing / total_cells,
                'final_missing_rate': cleaned_data.isnull().sum().sum() / (cleaned_data.shape[0] * cleaned_data.shape[1]),
                'columns_processed': len([log for log in operations_log if log['operation'] == 'missing_filled']),
                'columns_dropped': len([log for log in operations_log if log['operation'] == 'column_dropped'])
            }
        }
    
    def _detect_and_handle_outliers(self, data, config):
        """检测和处理异常值"""
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        outlier_summary = {}
        cleaned_data = data.copy()
        operations_log = []
        
        for column in numeric_columns:
            # 使用IQR方法检测异常值
            Q1 = data[column].quantile(0.25)
            Q3 = data[column].quantile(0.75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]
            outlier_count = len(outliers)
            
            if outlier_count > 0:
                outlier_rate = outlier_count / len(data)
                
                if outlier_rate < 0.05:  # 异常值比例小于5%，进行处理
                    # 用边界值替换异常值
                    cleaned_data.loc[cleaned_data[column] < lower_bound, column] = lower_bound
                    cleaned_data.loc[cleaned_data[column] > upper_bound, column] = upper_bound
                    
                    operations_log.append({
                        'operation': 'outlier_capped',
                        'column': column,
                        'outlier_count': outlier_count,
                        'outlier_rate': outlier_rate,
                        'lower_bound': lower_bound,
                        'upper_bound': upper_bound
                    })
                else:
                    # 异常值比例过高，仅记录不处理
                    operations_log.append({
                        'operation': 'outlier_detected_not_processed',
                        'column': column,
                        'outlier_count': outlier_count,
                        'outlier_rate': outlier_rate,
                        'reason': 'high_outlier_rate'
                    })
                
                outlier_summary[column] = {
                    'count': outlier_count,
                    'rate': outlier_rate,
                    'bounds': (lower_bound, upper_bound)
                }
        
        return {
            'cleaned_data': cleaned_data,
            'log_entries': operations_log,
            'metrics': {
                'columns_with_outliers': len(outlier_summary),
                'total_outliers_detected': sum(info['count'] for info in outlier_summary.values()),
                'outlier_summary': outlier_summary
            }
        }
```

### 2. 数据备份策略体系

#### 2.1 多层次备份架构
```python
import shutil
import json
import hashlib
from pathlib import Path
import schedule
import time

class DataBackupManager:
    def __init__(self, config_path='backup_config.json'):
        self.backup_strategies = {
            'full_backup': {
                'description': '完整备份',
                'frequency': 'weekly',
                'retention_period': '3_months',
                'compression': True,
                'encryption': True
            },
            'incremental_backup': {
                'description': '增量备份',
                'frequency': 'daily',
                'retention_period': '1_month',
                'compression': True,
                'encryption': False
            },
            'differential_backup': {
                'description': '差异备份',
                'frequency': 'hourly',
                'retention_period': '1_week',
                'compression': False,
                'encryption': False
            },
            'real_time_sync': {
                'description': '实时同步',
                'frequency': 'continuous',
                'retention_period': '24_hours',
                'compression': False,
                'encryption': True
            }
        }
        
        self.backup_locations = {
            'local_primary': {'type': 'local', 'path': './backups/primary/', 'priority': 1},
            'local_secondary': {'type': 'local', 'path': './backups/secondary/', 'priority': 2},
            'cloud_storage': {'type': 'cloud', 'provider': 'aws_s3', 'priority': 3},
            'remote_server': {'type': 'remote', 'host': 'backup.company.com', 'priority': 4}
        }
        
        self.load_config(config_path)
    
    def create_backup_plan(self, data_sources, business_requirements):
        """创建备份计划"""
        backup_plan = {
            'plan_id': self._generate_plan_id(),
            'created_at': datetime.now().isoformat(),
            'data_sources': data_sources,
            'backup_schedules': {},
            'recovery_objectives': {},
            'monitoring_config': {}
        }
        
        # 根据业务需求确定备份策略
        for source_name, source_config in data_sources.items():
            criticality = source_config.get('criticality', 'medium')
            data_size = source_config.get('size_gb', 1)
            change_frequency = source_config.get('change_frequency', 'daily')
            
            # 选择合适的备份策略组合
            selected_strategies = self._select_backup_strategies(
                criticality, data_size, change_frequency
            )
            
            backup_plan['backup_schedules'][source_name] = {
                'strategies': selected_strategies,
                'locations': self._select_backup_locations(criticality),
                'schedule_details': self._create_schedule_details(selected_strategies)
            }
        
        # 设置恢复目标
        backup_plan['recovery_objectives'] = self._define_recovery_objectives(business_requirements)
        
        # 配置监控
        backup_plan['monitoring_config'] = self._configure_backup_monitoring()
        
        return backup_plan
    
    def execute_backup(self, source_path, backup_type, destination):
        """执行备份操作"""
        backup_execution = {
            'backup_id': self._generate_backup_id(),
            'start_time': datetime.now(),
            'source_path': source_path,
            'backup_type': backup_type,
            'destination': destination,
            'status': 'in_progress',
            'metrics': {}
        }
        
        try:
            # 预备份检查
            pre_check_result = self._pre_backup_check(source_path, destination)
            if not pre_check_result['success']:
                raise Exception(f"Pre-backup check failed: {pre_check_result['error']}")
            
            # 执行具体备份操作
            if backup_type == 'full_backup':
                backup_result = self._execute_full_backup(source_path, destination)
            elif backup_type == 'incremental_backup':
                backup_result = self._execute_incremental_backup(source_path, destination)
            elif backup_type == 'differential_backup':
                backup_result = self._execute_differential_backup(source_path, destination)
            else:
                raise Exception(f"Unsupported backup type: {backup_type}")
            
            # 备份后验证
            verification_result = self._verify_backup(backup_result['backup_path'])
            
            backup_execution.update({
                'end_time': datetime.now(),
                'status': 'completed' if verification_result['success'] else 'failed',
                'backup_path': backup_result['backup_path'],
                'backup_size': backup_result['size'],
                'verification_result': verification_result,
                'metrics': {
                    'duration_seconds': (datetime.now() - backup_execution['start_time']).total_seconds(),
                    'compression_ratio': backup_result.get('compression_ratio', 1.0),
                    'files_backed_up': backup_result.get('file_count', 0)
                }
            })
            
        except Exception as e:
            backup_execution.update({
                'end_time': datetime.now(),
                'status': 'failed',
                'error': str(e),
                'metrics': {
                    'duration_seconds': (datetime.now() - backup_execution['start_time']).total_seconds()
                }
            })
        
        # 记录备份日志
        self._log_backup_execution(backup_execution)
        
        return backup_execution
    
    def _execute_full_backup(self, source_path, destination):
        """执行完整备份"""
        source = Path(source_path)
        backup_filename = f"full_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_path = Path(destination) / backup_filename
        
        # 创建备份目录
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # 复制所有文件
        if source.is_file():
            shutil.copy2(source, backup_path / source.name)
            file_count = 1
        else:
            shutil.copytree(source, backup_path / source.name, dirs_exist_ok=True)
            file_count = sum(1 for _ in (backup_path / source.name).rglob('*') if _.is_file())
        
        # 计算备份大小
        backup_size = sum(f.stat().st_size for f in backup_path.rglob('*') if f.is_file())
        
        # 创建备份元数据
        metadata = {
            'backup_type': 'full',
            'source_path': str(source),
            'backup_time': datetime.now().isoformat(),
            'file_count': file_count,
            'total_size': backup_size,
            'checksum': self._calculate_directory_checksum(backup_path)
        }
        
        with open(backup_path / 'backup_metadata.json', 'w') as f:
            json.dump(metadata, f, indent=2)
        
        return {
            'backup_path': str(backup_path),
            'size': backup_size,
            'file_count': file_count,
            'metadata': metadata
        }
    
    def _verify_backup(self, backup_path):
        """验证备份完整性"""
        verification_result = {
            'success': False,
            'checks_performed': [],
            'issues_found': []
        }
        
        backup_dir = Path(backup_path)
        
        # 检查备份目录是否存在
        if not backup_dir.exists():
            verification_result['issues_found'].append('Backup directory does not exist')
            return verification_result
        
        verification_result['checks_performed'].append('directory_existence')
        
        # 检查元数据文件
        metadata_file = backup_dir / 'backup_metadata.json'
        if metadata_file.exists():
            try:
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                # 验证文件数量
                actual_file_count = sum(1 for _ in backup_dir.rglob('*') if _.is_file() and _.name != 'backup_metadata.json')
                expected_file_count = metadata.get('file_count', 0)
                
                if actual_file_count != expected_file_count:
                    verification_result['issues_found'].append(
                        f'File count mismatch: expected {expected_file_count}, found {actual_file_count}'
                    )
                
                verification_result['checks_performed'].append('file_count_verification')
                
                # 验证校验和
                current_checksum = self._calculate_directory_checksum(backup_dir, exclude=['backup_metadata.json'])
                expected_checksum = metadata.get('checksum')
                
                if current_checksum != expected_checksum:
                    verification_result['issues_found'].append('Checksum verification failed')
                
                verification_result['checks_performed'].append('checksum_verification')
                
            except Exception as e:
                verification_result['issues_found'].append(f'Metadata verification error: {str(e)}')
        else:
            verification_result['issues_found'].append('Backup metadata file missing')
        
        verification_result['success'] = len(verification_result['issues_found']) == 0
        
        return verification_result
    
    def _calculate_directory_checksum(self, directory, exclude=None):
        """计算目录的校验和"""
        if exclude is None:
            exclude = []
        
        hash_md5 = hashlib.md5()
        
        for file_path in sorted(Path(directory).rglob('*')):
            if file_path.is_file() and file_path.name not in exclude:
                with open(file_path, 'rb') as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        hash_md5.update(chunk)
        
        return hash_md5.hexdigest()
```

## ✅ 实施检查清单

### 数据清洗流程检查清单
- [ ] 建立数据质量评估标准和指标
- [ ] 设计自动化的数据清洗流水线
- [ ] 配置异常值检测和处理规则
- [ ] 建立数据清洗操作的日志记录
- [ ] 设置数据清洗效果的验证机制

### 备份策略检查清单
- [ ] 确定数据的重要性分级和备份需求
- [ ] 设计多层次的备份架构和策略
- [ ] 配置自动化的备份调度和执行
- [ ] 建立备份完整性验证机制
- [ ] 设置备份监控和异常报警

### 数据安全检查清单
- [ ] 实施数据访问控制和权限管理
- [ ] 配置数据加密和安全传输
- [ ] 建立数据合规和隐私保护机制
- [ ] 设置数据恢复测试和演练
- [ ] 建立数据生命周期管理流程

---

**下一步学习建议**：
完成本模块后，建议继续学习"洞察生成与价值转化.md"，这是整个信息分析流程的最终环节，将帮助您把高质量的数据转化为可操作的业务洞察。
