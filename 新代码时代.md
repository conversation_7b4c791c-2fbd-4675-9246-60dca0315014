# 新代码时代

**演讲者：Sean Grove，OpenAI**  
**原视频：** https://www.youtube.com/watch?v=8rABwKRsec4

---

## 引言

大家好，非常感谢邀请我来到这里。这是一个令人兴奋的地方，也是一个令人兴奋的时代。过去几天非常紧张，但也充满活力。今天我想花一些时间与大家讨论我所看到的"新代码时代"的到来。

特别是规范（specifications），它承载着一个长期以来的行业梦想：你可以编写一次代码和意图，然后在任何地方运行它们。

我是Sean，在OpenAI从事对齐研究工作。今天我想谈论代码与沟通的价值，以及为什么规范通常可能是一种更好的方法。

## 演讲大纲

我将讨论以下内容：
- 规范的结构，以模型规范为例
- 如何向其他人类传达意图
- 以"过度迎合"问题为案例研究
- 如何使规范可执行
- 如何向模型传达意图
- 如何将规范视为代码
- 一些开放性问题

---

## 代码 vs 沟通

让我先问一个问题：在座的各位，有多少人写代码？（包括"氛围编程"）请举手。保持举手状态，如果你的工作就是写代码的话。现在，对于那些举手的人，如果你认为你产出的最有价值的专业成果是代码，请继续举手。

我看到相当多的人举手，这很自然。我们都非常努力地解决问题。我们与人交谈，收集需求，思考实现细节，与各种不同的来源集成。我们最终产出的是代码。代码是我们可以指向、可以衡量、可以辩论和讨论的成果。

它感觉起来是有形的、真实的，但这实际上低估了你们每个人所做的工作。**代码只占你们带来价值的10-20%，其他80-90%都在于结构化沟通。**

## 结构化沟通的价值

虽然每个人的情况不同，但典型的流程通常是这样的：

1. **与用户交谈** - 了解他们的挑战
2. **提炼故事** - 将这些故事归纳总结
3. **构思解决方案** - 思考如何解决这些问题，确定要达成的目标
4. **制定计划** - 规划实现这些目标的方法
5. **分享计划** - 与同事分享这些计划
6. **转化为代码** - 将计划转化为代码（这是重要的一步）
7. **测试和验证** - 不是测试代码本身，而是测试代码运行时是否达成了目标，是否缓解了用户的挑战

你关心的是代码对世界产生的影响。

交谈、理解、提炼、构思、规划、分享、转化、测试、验证——这些对我来说都像是结构化沟通。

**结构化沟通是瓶颈所在。** 知道要构建什么，与人交谈并收集需求，知道如何构建，知道为什么要构建，最终知道是否正确构建并真正实现了你设定的意图。

随着AI模型变得越来越先进，我们都将更加明显地感受到这个瓶颈。因为在不久的将来，**最有效沟通的人将成为最有价值的程序员。** 从字面上讲，如果你能有效沟通，你就能编程。

## 氛围编程的启示

让我们以氛围编程为例。氛围编程往往感觉很好，值得问问为什么？

氛围编程从根本上说是**沟通优先的**，代码实际上是该沟通的次要下游产物。我们可以描述我们的意图和想要看到的结果，让模型为我们处理繁重的工作。

但是，我们进行氛围编程的方式有些奇怪。我们通过提示与模型沟通，告诉它们我们的意图和价值观，最后得到代码产物，然后我们就把提示扔掉了——它们是短暂的。

如果你写过TypeScript或Rust，一旦你把代码通过编译器编译或转换成二进制文件，没有人会对那个二进制文件感到满意。那不是目的。它是有用的，但我们总是每次编译时从源规范重新生成二进制文件，或者通过V8等运行代码。**源规范才是有价值的产物。**

然而，当我们提示模型时，我们做的是相反的事情。我们保留生成的代码，删除提示。这感觉有点像你撕碎了源代码，然后非常仔细地对二进制文件进行版本控制。

## 规范的重要性

这就是为什么在规范中捕获意图和价值观如此重要。**书面规范使你能够让人类在共同目标集上保持一致，并知道你们是否真正同步了需要完成的工作。**

这是你讨论、辩论、参考和同步的产物。这非常重要。

**书面规范有效地使人类保持一致，它是你用来沟通、讨论、辩论、参考和同步的产物。**

如果你没有规范，你只有一个模糊的想法。

## 为什么规范比代码更强大

规范通常比代码更强大，因为**代码本身实际上是规范的有损投影**。就像如果你拿一个编译的C二进制文件并反编译它，你不会得到好的注释和命名良好的变量。你必须反向工作，推断这个人试图做什么，为什么代码是这样写的。这些信息实际上并不包含在其中，这是一个有损的翻译。

同样，代码本身，即使是好的代码，通常也不能体现所有的意图和价值观。当你阅读代码时，你必须推断这个团队试图实现的最终目标是什么。

**沟通——我们在书面规范中体现的工作——比代码更好。** 它实际上编码了生成代码所需的所有必要需求。

就像拥有传递给编译器的源代码允许你针对多个不同的架构一样——你可以为ARM 64、x86或WebAssembly编译——源文档实际上包含足够的信息来描述如何将其转换为目标架构。

同样，**给予模型的足够健壮的规范将产生良好的TypeScript、Rust、服务器、客户端、文档、教程、博客文章，甚至播客。**

## 思考实验

让我问在座的各位：有多少人在为开发者提供服务的公司工作？

这里有一个快速的思考实验：如果你把整个代码库、所有文档——运行你业务的所有代码——放入播客生成器，你能生成足够有趣和引人注目的内容，告诉用户如何成功、如何实现他们的目标吗？还是所有这些信息都在别的地方？它实际上不在你的代码中。

**因此，向前发展，新的稀缺技能是编写完全捕获意图和价值观的规范。掌握这一点的人将成为最有价值的程序员。**

很有可能这将是今天的编码者。这已经与我们所做的非常相似。然而，产品经理也写规范，立法者写法律规范。这实际上是一个普遍原则。

---

## 规范的结构：以OpenAI模型规范为例

考虑到这一点，让我们看看规范实际上是什么样子的。我将使用OpenAI模型规范作为例子。

去年，OpenAI发布了模型规范。这是一个活文档，试图清晰明确地表达OpenAI希望赋予其向世界发布的模型的意图和价值观。它在二月份更新并开源。你可以在GitHub上看到模型规范的实现，令人惊讶的是，它实际上只是一个markdown文件的集合。

**Markdown是了不起的。** 它是人类可读的，有版本控制，有变更日志，因为它是自然语言，不仅仅是技术人员，包括产品、法律、安全、研究、政策部门的每个人都可以贡献，他们都可以阅读、讨论、辩论并为同一个源代码做出贡献。

**这是使公司内所有人类在我们的意图和价值观上保持一致的通用产物。**

## 处理模糊性

尽管我们可能试图使用明确的语言，但有时很难表达细微差别。因此，模型规范中的每个条款都有一个ID。使用该ID，你可以在存储库中找到另一个文件，其中包含针对该确切条款的一个或多个具有挑战性的提示。

**文档本身实际上编码了成功标准，被测试的模型必须能够以实际遵守该条款的方式回答这个问题。**

## 案例研究：过度迎合问题

最近GPT-4有一个更新，引起了极度的过度迎合。我们可以问，在这种情况下模型规范的价值是什么？

模型规范用于使人类围绕一套价值观和意图保持一致。这里有一个过度迎合的例子，用户指出了以牺牲公正真理为代价的过度迎合或奉承行为，模型非常友善地赞扬了用户的洞察力。

其他受尊敬的研究人员也发现了类似令人担忧的例子。以这种方式发布过度迎合会侵蚀信任，这是有害的。这也提出了很多问题：这是故意的吗？这是意外的，为什么没有被发现？

幸运的是，**模型规范实际上包含了一个专门针对此问题的部分**，自发布以来就说"不要过度迎合"，它解释说虽然过度迎合在短期内可能感觉良好，但从长远来看对每个人都不好。

所以我们实际上表达了我们的意图和价值观，并能够通过这个向其他人传达。人们可以参考它，如果我们在模型规范中有它，如果模型规范是我们商定的意图和价值观集合，而行为与此不一致，那么这必须是一个错误。

所以我们回滚了，发布了一些研究和博客文章，并修复了它。但在此期间，**规范作为信任锚点，向人们传达什么是预期的，什么是不预期的。**

如果模型规范唯一做的事情就是使人类沿着那些共同的意图和价值观集合保持一致，它就已经非常有用了。

## 使规范可执行

但理想情况下，我们也可以使我们的模型和模型产生的产物与同一规范保持一致。

有一种技术，我们发布的一篇论文叫做"深思熟虑的对齐"，讨论了如何自动对齐模型。技术是这样的：你拿你的规范和一组非常具有挑战性的输入提示，从被测试或训练的模型中采样。

然后你拿它的响应、原始提示和政策，给一个评分模型，要求它根据规范对响应进行评分。它有多对齐？

**文档实际上既成为训练材料又成为评估材料**，基于这个分数我们强化那些权重。你可以在上下文中包含你的规范，然后可能在每次采样时都有系统消息或开发者消息，这实际上非常有用。

提示的模型将在某种程度上对齐，但它确实会减少可用于解决你试图用模型解决的问题的计算。记住，这些规范可以是任何东西。它们可以是代码风格或测试要求或安全要求。所有这些都可以嵌入到模型中。

通过这种技术，你实际上将其从推理时计算转移，实际上你将其推入模型的权重中，使模型实际上感受到你的政策，并能够以肌肉记忆的方式将其应用到手头的问题上。

## 规范即代码

即使我们看到模型规范只是markdown，将其视为代码是非常有用的。它非常类似。

这些规范：
- **可组合** - 它们可以组合在一起
- **可执行** - 如我们所见
- **可测试** - 它们有测试用例
- **有接口** - 它们与现实世界接触
- **可作为模块发布** - 可以模块化部署

每当你在处理模型规范时，都有很多类似的问题域。就像在编程中你有类型检查器一样，类型检查器旨在确保一致性，如果接口A有依赖模块B，它们必须在彼此的理解上保持一致。

如果部门A写了一个规范，部门B写了一个规范，其中存在冲突，你希望能够提前发现这一点，也许阻止规范的发布。

如我们所见，政策实际上可以体现自己的单元测试，你可以想象各种linter，如果你使用过于模糊的语言，你会混淆人类，也会混淆模型，从中得到的产物将不太令人满意。

**所以规范实际上给了我们一个非常相似的工具链，但它针对的是意图而不是语法。**

## 立法者即程序员

让我们谈谈立法者作为程序员。**美国宪法字面上就是一个国家模型规范。** 它有书面文本，至少在理想情况下是清晰明确的政策，我们都可以参考。

这并不意味着我们同意它，但我们可以将其作为当前现状、现实来参考。有一种版本化的方式来进行修正，发布更新。有司法审查，评分者有效地对情况进行评分，看它与政策的对齐程度如何。

即使源政策意图是明确的，有时世界是混乱的，也许你错过了分布的一部分，案例漏掉了，在这种情况下，司法审查中花费了大量计算，试图理解法律在这里实际如何适用，一旦决定，它就设定了先例，该先例有效地是一个输入输出对，作为单元测试，消除歧义并强化原始政策规范。

它有嵌入其中的指挥链，随着时间的推移，这种执行是一个训练循环，帮助我们所有人朝着共同的意图和价值观集合对齐。

**这是一个传达意图的产物。它裁决合规性，并有一种安全演进的方式。**

很可能立法者将成为程序员，或者相反，程序员将在未来成为立法者。

## 通用概念

实际上这是一个非常通用的概念：
- **程序员** 通过代码规范对齐硅
- **产品经理** 通过产品规范对齐团队  
- **立法者** 通过法律规范对齐人类
- **在座的每个人** 每当你做提示时，这是一种原型规范

你们都在将AI模型对齐到共同的意图和价值观集合的业务中。无论你是否意识到，**你们都是这个世界中的规范作者**。

规范让你更快更安全地发布。每个人都可以贡献，无论是PM、立法者、工程师还是营销人员，写规范的人现在就是程序员。

## 软件工程的本质

**软件工程从来不是关于代码的。** 回到我们最初的问题，当你们想到实际产出的不是代码时，很多人放下了手。工程从来不是关于这个的。

编码是一项令人难以置信的技能和宝贵的资产，但它不是最终目标。**工程是人类对人类问题的软件解决方案的精确探索。**

一直都是这样。我们只是从分散的机器编码转向统一的人类编码，来实际解决这些问题。

## 行动建议

我想请你们付诸行动。每当你在开发下一个AI功能时，**从规范开始**：

1. **明确期望** - 你实际期望发生什么？
2. **定义成功标准** - 成功标准是什么样的？
3. **辩论清晰度** - 辩论它是否实际清晰地写下并传达了
4. **使规范可执行** - 将规范提供给模型
5. **测试对照规范** - 对模型进行测试或对照规范进行测试

## 未来的IDE

在这个世界中，考虑到编程和规范编写之间有如此多的相似之处，我想知道未来的IDE会是什么样子。你知道，集成开发环境。

我想它可能像一个**集成思维澄清器**，每当你写规范时，它会提取模糊性并要求你澄清，真正澄清你的思维，这样你和所有人类都能更有效地向彼此和模型传达你的意图。

## 结语：寻求帮助

我有一个结束请求，寻求帮助：什么既适合又迫切需要规范？这就是**大规模对齐智能体**。

我喜欢这句话："然后你意识到你从未告诉它你想要什么，也许你从来没有完全理解过。"这是对规范的呼唤。

我们启动了一个新的智能体鲁棒性团队。请加入我们，帮助我们为全人类的利益提供安全的AGI。

谢谢大家。我很乐意聊天。

---

*本文档基于Sean Grove在OpenAI的演讲整理翻译，旨在探讨AI时代编程范式的转变，从传统的代码编写转向规范驱动的开发方式。*
